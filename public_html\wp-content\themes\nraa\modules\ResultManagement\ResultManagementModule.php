<?php use NRAA\Exceptions\NRAAException;

defined('ABSPATH') or die('No direct script access.');

/**
 * Module - Result Management
 */
class ResultManagementModule extends Module implements IModule
{
  public $router;
  protected $views_directory;
  protected $topic;

  public function __construct($skip_roles = false, $topic = null)
  {
    parent::__construct($skip_roles);

    $this->topic = $topic;
    $this->views_directory = __DIR__ . '/views/';
    $this->router          = new SimpleRouter(array(
      'base_url' => 'admin.php?page=' . basename(__FILE__)
    ));

    $this->router->setRoute('', array($this, 'index'))
      ->setRoute('select-match', array($this, 'select_match'))
      ->setRoute('list-results', array($this, 'list_results'))
      ->setRoute('edit-result', array($this, 'edit_result'))
      ->setRoute('edit-result-fast', array($this, 'edit_result_fast'))
      ->setRoute('edit-result-teams-fast', array($this, 'edit_result_teams_fast'))
      ->setRoute('delete-result', array($this, 'delete_result'))
      ->setRoute('standard-report', array($this, 'generate_standard_report'))
      ->setRoute('standard-report-csv', array($this, 'generate_standard_report_csv'))
      ->setRoute('summary-team-report', array($this, 'generate_summary_team_report'))
      ->setRoute('detailed-team-report', array($this, 'generate_detailed_team_report'))
      ->setRoute('presentation-order', array($this, 'presentation_order'))
      ->setRoute('presentation-report-by-match', array($this, 'generate_presentation_report_by_match'))
      ->setRoute('presentation-report-by-id', array($this, 'generate_presentation_report_by_id'))
      ->setRoute('presentation-report-by-division', array($this, 'generate_presentation_report_by_division'))
      ->setRoute('presentation-team-report', array($this, 'generate_presentation_team_report'))
      ->setRoute('publish-match', array($this, 'publish_match'))
      ->setRoute('publish-results', array($this, 'publish_results'))
      ->setRoute('calc-places', array($this, 'calc_places'))
      ->setRoute('calc-aggregates', array($this, 'calc_aggregates'))
      ->setRoute('recalculate-matches', array($this, 'recalculate_matches'))
      ->setRoute('divisional-report', array($this, 'divisional_report'))
      ->setRoute('divisional-report-csv', array($this, 'divisional_report_csv'))
      ->setRoute('select-shooters-for-special', array($this, 'select_shooters_for_special'))
      ->setRoute('upload-hexta', array($this, 'upload_hexta'))
      ->setRoute('scoreboard', array($this, 'scoreboard'));
  }

  public function index()
  {
    $pagination = new Pagination(
      Database::factory('Event')
        ->where('start_date', '<', strtotime('+2 days'))
        ->order_by('start_date', 'DESC'),
      $this->router->getUrl('')
    );
    $pagination->setSearchFields(array('id', 'name'));

    View::load($this->views_directory, 'event_select', array(
      'router'     => $this->router,
      'pagination' => $pagination,
    ));
  }

  public function presentation_order()
  {
    $eventId = Arr::get($_GET, 'event_id');
    $returnAction = Arr::get($_GET, 'return-action');

    $sections = Database::factory('Grade')
      ->join('EventPresentationOrdering', 'LEFT OUTER', false, 'epo')
      ->on('epo', 'grades.id', '=', 'epo.grade_id')
      ->on('epo', 'epo.event_id', '=', $eventId)
      ->order_by('epo.order', 'ASC')
      ->order_by('grades.id')
      ->find_all();

    View::load($this->views_directory, 'order_presentation_report', array(
      'router'        => $this->router,
      'event_id'      => $eventId,
      'returnAction'  => $returnAction,
      'sections'      => $sections,
    ));
  }

  /**
   * FIXME: THIS CODE IS TEMPORARY AND SHOULD BE MOVED TO COOL-SERVER
   */
  public function scoreboard()
  {
    $eventId = Arr::get($_GET, 'event_id');
    $subeventId = Arr::get($_GET, 'subevent_id');
    $event = Database::factory('Event', $eventId)->find();
    $subevent = Database::factory('SubEvent', $subeventId)->find();

    if (!$event->is_loaded() || !$event->is_competition || !$event->is_using_entry_form) {
      $this->do_404();
      return;
    }
    $results = Database::factory('Result')
      ->join('MatchModel', null, true)
      ->on('MatchModel', 'matches.id', '=', 'results.match_id')
      ->join('Grade', null, true)
      ->on('Grade', 'grades.id', '=', 'results.grade_id')
      ->join('Shooter', null, true)
      ->on('Shooter', 'shooters.id', '=', 'results.shooter_id')
      ->join('ShooterDetails', null, true)
      ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'shooters.id')
      ->join('EventEntryForm', null, true)
      ->on('EventEntryForm', 'event_entry_forms.shooter_id', '=', 'shooters.id')
      ->on('EventEntryForm', 'event_entry_forms.event_id', '=', 'matches.event_id')
      ->where('matches.event_id', '=', $eventId)
      ->where('matches.is_cancelled', '=', 0)
      ->where('IsAggregate(matches.id)', '=', 0)
      ->order_by('grades.id', 'ASC')
      ->order_by('shooters.id', 'ASC');

    if ($subeventId) {
      $results = $results
        ->join('Match_Range', null, true)
        ->on('Match_Range', 'match_ranges.match_id', '=', 'matches.id')
        ->join('SubEvent_Range', null, true)
        ->on('SubEvent_Range', 'subevents_ranges.range_id', '=', 'match_ranges.range_id')
        ->where('subevents_ranges.subevent_id', '=', $subeventId);
    }

    $results = $results->find_all_as_array();

    $data = array();
    $disciplines = array();
    foreach ($results as $result) {
      $gradeData = Arr::get($data, $result['grade_id'], array());
      $shooterData = Arr::get($gradeData, $result['Shooter.id'], (object) array(
        'id' => $result['Shooter.id'],
        'grade_id' => $result['grade_id'],
        'place' => 0,
        'score' => new Score(),
        'matchData' => array(),
        'shots' => '',
        'entryId' => $result['EventEntryForm.id'],
        'entryNumber' => $result['EventEntryForm.entry_number'],
        'sid' => $result['Shooter.sid'],
        'shooterName' => sprintf(
          '%s %s',
          $result['ShooterDetails.preferred_name']
            ? $result['ShooterDetails.preferred_name']
            : $result['ShooterDetails.first_name'],
          $result['ShooterDetails.last_name']),
      ));
      $score = new Score($result['score_whole'], $result['score_partial']);
      array_push($shooterData->matchData, array(
        'match_id' => $result['Match.id'],
        'number' => $result['Match.number'],
        'score' => $score,
        'shots' => $result['shots']
      ));
      $shooterData->score = $shooterData->score->add($score);
      $shooterData->shots .= $result['shots'];
      $gradeData[$result['Shooter.id']] = $shooterData;
      $data[$result['grade_id']] = $gradeData;
      if (!isset($disciplines[$result['Grade.discipline_id']])) {
        $disciplines[$result['Grade.discipline_id']] = Database::factory('Discipline', $result['Grade.discipline_id'])->find();
      }
    }
    foreach ($data as &$gradeData) {
      usort($gradeData, function ($a, $b) {
        $sort = $a->score->compare($b->score);
        if ($sort === 0) { // Tie
          $comparer = function ($curMatch, $nextMatch) {
            return $nextMatch['number'] - $curMatch['number'];
          };
          usort($a->matchData, $comparer);
          usort($b->matchData, $comparer);
          $matchCount = count($a->matchData);
          for ($i = 0; $i < $matchCount; $i++) {
            $sort = $a->matchData[$i]['score']->compare($b->matchData[$i]['score']);
            if ($sort !== 0) { // Winnar
              return $sort;
            }
          }
          // Check shots
          $shotValues = array('0' => 0.0, '1' => 1.0, '2' => 2.0, '3' => 3.0, '4' => 4.0, '5' => 5.0, 'V' => 5.1, '6' => 6.0, 'X' => 6.1);
          $aShots     = array_reverse(str_split($a->shots));
          $bShots     = array_reverse(str_split($b->shots));
          foreach ($aShots as $i => $v) {
            if ($bShots[$i] != $v)
              return $shotValues[$v] - $shotValues[$bShots[$i]];
          }
        }
        return $sort;
      });
      // Setup placements
      $placeMover = 1;
      $tiedCount = 0;
      $numberOfResults = count($gradeData);
      for ($i = 0; $i < $numberOfResults; $i++) {
        $result = $gradeData[$i];
        $gradeData[$i]->place = $result->place + $placeMover;

        if ($i + 1 < $numberOfResults) {
          $nextResult = $gradeData[$i+1];
          // Daily aggregate and Aggregate of daily aggregate don't count back first place
          if (($nextResult->shots === $result->shots) || ($result->place === 1 && $nextResult->place == 1)) {
            $tiedCount++;
          } else {
            $placeMover += $tiedCount + 1;
            $tiedCount = 0;
          }
        }
      }
    }
    usort($disciplines, function ($a, $b) {
      return $a->order - $b->order;
    });

    View::load($this->views_directory, 'scoreboard', array(
      'router' => $this->router,
      'eventId' => $eventId,
      'subeventId' => $subeventId,
      'subevent' => $subevent,
      'subevents' => Database::factory('SubEvent')->where('event_id', '=', $eventId)->find_all(),
      'disciplines' => $disciplines,
      'data' => $data
    ));
  }

  public function select_match()
  {
    $this->handle_post($_POST);

    $selected_event = Database::factory('Event', Arr::get($_GET, 'event_id'))->find();
    if (!$selected_event->is_loaded()) {
      $this->do_404();
      return;
    }
    $matches = Database::factory('MatchModel')
      ->where('event_id', '=', $selected_event->id)
      ->order_by('number')
      ->find_all();
    $isPublished = count(
        query_posts(
          array(
            'post_type'  => 'page',
            'meta_query' => array(
              array(
                'key' => '_wp_page_template',
                'value' => 'templates/results.php'
              ),
              array(
                'key' => 'event_id',
                'value' => $selected_event->id
              ),
            )
          )
        )
      ) > 0;
    $hasMatchesPublished = Database::factory('MatchModel')
        ->where('event_id', '=', $selected_event->id)
        ->where('is_published', '=', '1')
        ->count_all() > 0;
    if ($selected_event->is_using_entry_form) {
      $specialSubevents = Database::factory('SubEvent')
        ->where('event_id', '=', $selected_event->id)
        ->where('special_rules', 'IS NOT', 'NULL')
        ->find_all();
    } else {
      $specialSubevents = array();
    }

    View::load($this->views_directory, 'match_select', array(
      'router'              => $this->router,
      'selected_event'      => $selected_event,
      'matches'             => $matches,
      'isPublished'         => $isPublished,
      'hasMatchesPublished' => $hasMatchesPublished,
      'specialSubevents'    => $specialSubevents,
      'views'               => $this->views_directory
    ));
  }

  protected function handle_post($post)
  {
    if (!$post)
      return;

    $id = Arr::get($post, 'id');
    if (isset($post['save'])) {
      $isAutoCalcPlaces = false;
      $isAutoCalcAggregates = false;
      if (isset($post['auto_calc_places'])) {
        $isAutoCalcPlaces = $post['auto_calc_places'] == '1';
        unset($post['auto_calc_places']);
      }
      if (isset($post['auto_calc_aggregates'])) {
        $isAutoCalcAggregates = $post['auto_calc_aggregates'] == '1';
        unset($post['auto_calc_aggregates']);
      }
      $save = $post['save'];
      unset($post['save']);

      switch ($save) {
      case 'save-places':
        foreach ($post['result'] as $id => $data) {
          $result = Database::factory('Result', $id)->find();
          if (!$result->is_loaded())
            throw new Exception("Result not found!");
          $result->place = $data['place'];
          $result->save();
        }
        add_message('Places updated');
        return;

      case 'save-team-places':
        foreach ($post['result'] as $id => $data) {
          $result = Database::factory('TeamResult', $id)->find();
          if (!$result->is_loaded())
            throw new Exception("Result not found!");
          $result->place = $data['place'];
          $result->save();
        }
        unset($_GET['edit-team']);
        add_message('Team Places updated');
        return;

      case 'presentation-order':
        $jsonStr = str_replace('section-', '', $post['sectionOrder']);
        $jsonStr = str_replace('\\', '', $jsonStr);
        $sectionIds = json_decode($jsonStr);

        $event = new Event(Arr::get($_GET, 'event_id'));
        $event->deletePresentationOrderings();

        foreach ($sectionIds as $index => $sectionId) {
          $ordering = new EventPresentationOrdering();
          $ordering->event_id = $event->id;
          $ordering->grade_id = $sectionId;
          $ordering->order = $index + 1;
          $ordering->save();
        }
        add_message('Presentation report order updated');
        return;

      case 'result-fast':
        $match_id = $post['match_id'];
        $jsonData = json_decode(stripslashes($post['data']));
        foreach ($jsonData as $row) {
          if (!$row->score->shots && !$row->score->scoreWhole)
            continue;
          $result = Database::factory('Result', $row->id);
          if ($row->id)
            $result->find();
          else {
            // find out if this result has already been submitted
            $check = Database::factory('Result')
              ->where('match_id', '=', $match_id)
              ->where('shooter_id', '=', $row->shooter->id)
              ->where('grade_id', '=', $row->grade->id)
              ->where('score_whole', '=', $row->score->scoreWhole)
              ->where('score_partial', '=', $row->score->scorePartial)
              ->find();
            if ($check->is_loaded())
              return;
          }
          $result->match_id   = $match_id;
          $result->shooter_id = $row->shooter->id;
          $result->grade_id   = $row->grade->id;
          $result->place      = 0;
          if ($row->score->shots)
            $result->shots = $row->score->shots;
          $result->score_whole   = $row->score->scoreWhole;
          $result->score_partial = $row->score->scorePartial;
          $result->save();
        }
        add_message('Results saved' . ' ' . $match_id);

        $this->performAutomatedCalculations(
          $isAutoCalcAggregates,
          $isAutoCalcPlaces,
          $post['event_id'],
          [$match_id]
        );
        return;

        case 'select-shooters':
          if (isset($post['subevent_id']) && is_array($post['selected'])) {
            $subevent = Database::factory('SubEvent', $post['subevent_id'])->find();
            if (!$subevent->is_loaded()) {
              $this->do_404();
              return;
            }
            Database::factory('EventEntryFormSubEvent')
              ->where('subevent_id', '=', $post['subevent_id'])
              ->delete();
            foreach ($post['selected'] as $row) {
              if (isset($row['entry_id']) && isset($row['grade_id'])) {
                Database::factory('EventEntryFormSubEvent')
                  ->values(array(
                    'event_entry_form_id' => $row['entry_id'],
                    'subevent_id'         => $post['subevent_id'],
                    'grade_id'            => $row['grade_id']
                  ))
                  ->save();
              }
            }
          }
          return;

      default:
        // Quick and Dirty Validation
        if (empty($post['shooter_id'])
          || empty($post['grade_id'])
          || empty($post['score'])
        ) {
          return;
        }

        if (strpos($post['score'], '.') !== false)
          list($post['score_whole'], $post['score_partial']) = explode('.', $post['score']);
        else
          $post['score_whole'] = $post['score'];

        unset($post['score']);
        Database::factory('Result', $id)
          ->find()
          ->values($post)
          ->save();

        add_message('Result saved');

        $this->performAutomatedCalculations(
          $isAutoCalcAggregates,
          $isAutoCalcPlaces,
          $post['event_id'],
          [$post['match_id']]);
        return;
      }
    } elseif (isset($post['delete'])) {
      Database::factory('Result', $id)->delete();
      add_message('Result deleted');
    }
  }

  public function calculate_score($shots) {
    $scoreWhole = 0;
    $scorePartial = 0;
    $shots = strtoupper($shots);
    foreach (str_split($shots) as $shot) {
      if ($shot == 'V' || $shot == 'X') {
        $scorePartial += 1;
        $scoreWhole += $shot == 'V' ? 5 : 6;
      } else {
        $scoreWhole += intval($shot);
      }
    }
    return new Score($scoreWhole, $scorePartial);
  }

  public function upload_hexta()
  {
    $selected_event = Database::factory('Event', Arr::get($_GET, 'event_id'))->find();
    if (!$selected_event->is_loaded()) {
      $this->do_404();
      return;
    }

    View::load($this->views_directory, 'upload_hexta', array(
      'router'          => $this->router,
      'views'           => $this->views_directory,
      'selected_event'  => $selected_event,
    ));
  }

  public function list_results()
  {
    $this->handle_post($_REQUEST);

    $event_id       = Arr::get($_REQUEST, 'event_id');
    $match_id       = Arr::get($_REQUEST, 'match_id');
    $selected_event = Database::factory('Event', $event_id)->find();
    $selected_match = Database::factory('MatchModel', $match_id)->find();

    if (!($event_id && $selected_event->is_loaded()
    && $match_id && $selected_match->is_loaded())) {
      $this->do_404();
      return;
    }

    $all_results = Database::factory('Result')
      ->where('match_id', '=', $match_id)
      ->order_by('grade_id')
      ->order_by('score_whole', 'desc')
      ->order_by('score_partial', 'desc')
      ->order_by('place');

    $all_results = $selected_event->is_divisional
      ? $all_results->find_all_divisional()
      : $all_results->find_all();

    $grades   = array();
    $has_ties = false;
    foreach ($all_results as $result) {
      if ($result->is_tied)
        $has_ties = true;

      if ($selected_event->is_divisional) {
        $division = $result->division;
        if (!$division->id) {
          $division = Database::factory("Division")
            ->order_by("divisions.order", "asc")
            ->limit(1)
            ->find_all();
          $division = array_pop($division);
        }
        if (array_key_exists($division->id, $grades)) {
          $grade = $grades[$division->id];
          $grade->results[$result->id] = $result;
        } else {
          $division->results = array($result->id => $result);
          $grades[$division->id] = $division;
        }
      } else if (!array_key_exists($result->grade_id, $grades)) {
        $grade = $result->grade;
        $grade->results = array($result->id => $result);
        $grades[$grade->id] = $grade;
      } else {
        $grade = $grades[$result->grade_id];
        $grade->results[$result->id] = $result;
      }
    }

    if ($selected_event->is_divisional) {
      usort($grades, function ($a, $b) {
        if ($a->order == $b->order) {
          return $a->id < $b->id ? 1: -1;
        }
        return $a->order < $b->order ? -1 : 1;
      });
    }

    $teamResults = null;
    $teamsForDropDown = null;
    if ($selected_event->is_team_event) {
      $teamResults = array();

      $teams = Database::factory('EventTeam')
        ->join('EventTeam_Shooter')
        ->on('EventTeam_Shooter', 'event_teams_shooters.team_id', '=', 'event_teams.id')
        ->where('event_id', '=', $event_id)
        ->find_all();

      foreach ($teams as $team) {
        $teamResult = Database::factory('TeamResult')
          ->where('match_id', '=', $selected_match->id)
          ->where('team_id', '=', $team->id)
          ->find();

        if (!$teamResult->grade_id)
          continue;

        if (!isset($teamResults[$teamResult->grade_id]))
          $teamResults[$teamResult->grade_id] = array();
        $teamResults[$teamResult->grade_id][] = $teamResult;
      }

      $teamsForDropDown = Database::factory('EventTeam')
        ->join('EventTeam_Shooter')
        ->on('EventTeam_Shooter', 'event_teams_shooters.team_id', '=', 'event_teams.id')
        ->where('event_id', '=', $event_id)
        ->find_all_as_array('name');
    }

    View::load($this->views_directory, 'result_list', array(
      'router'             => $this->router,
      'grades'             => $grades,
      'has_ties'           => $has_ties,
      'editing_ties'       => Arr::get($_GET, 'edit'),
      'editing_tied_teams' => Arr::get($_GET, 'edit-team'),
      'selected_match'     => $selected_match,
      'selected_event'     => $selected_event,
      'teamResults'        => $teamResults,
      'teams'              => $teamsForDropDown,
      'associations'       => Database::factory('Association')->find_all_as_array('name')
    ));
  }

  public function edit_result()
  {
    $this->handle_post($_POST);

    if (Arr::get($_POST, 'save') === 'result-close') {
      $this->do_redirect(
        $this->router->getUrl('list-results',
        array(
          'event_id' => Arr::get($_REQUEST, 'event_id'),
          'match_id' => Arr::get($_REQUEST, 'match_id')
        ))
      );
      return;
    }

    $result = Database::factory('Result', Arr::get($_GET, 'result_id'));
    $match  = Database::factory('Match', Arr::get($_GET, 'match_id', $result->match_id));

    // All shooters that have not already shot in this match
    $not_in = "(SELECT shooter_id FROM results WHERE match_id = {$match->id}" .
      ($result->is_loaded() && $result->shooter_id
        ? " AND shooter_id != {$result->shooter_id})"
        : ')');
    $shooters_array = Database::factory('Shooter')
      ->join('ShooterDetails')
      ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'shooters.id')
      ->where('shooters.id', 'NOT IN', $not_in)
      ->find_all_as_array_cb(function ($shooter) {
        return sprintf(
          '%s - %s %s - %s',
          $shooter['sid'] ? $shooter['sid'] : $shooter['id'],
          $shooter['preferred_name'] ? $shooter['preferred_name'] : $shooter['first_name'],
          $shooter['last_name'],
          substr($shooter['club'], 0, 8)
        );
      });

    $grades = Database::factory('Grade')->find_all();
    $grades_array = array(0 => null,);
    foreach ($grades as $grade)
      $grades_array[$grade->id] = sprintf('%s - %s', $grade->discipline->name, $grade->name);
    $event = Database::factory('Event', Arr::get($_GET, 'event_id'));
    $entryForms = null;

    if ($event->is_using_entry_form) {
      $db = Database::factory('EventEntryForm')
        ->join('ShooterDetails')
        ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'event_entry_forms.shooter_id')
        ->where('event_id', '=', $event->id)
        ->where('event_entry_forms.shooter_id', 'NOT IN', $not_in)
        ->find_all_as_array_cb(function ($entryForm) {
          return sprintf(
            '[%s] %s %s',
            $entryForm['entry_number'],
            $entryForm['preferred_name'] ? $entryForm['preferred_name'] : $entryForm['first_name'],
            $entryForm['last_name']
          );
        });
      $entryForms = array_unshift_assoc($db, 0, null);
      $result->entry_form = ($result->is_loaded())
        ? Database::factory('EventEntryForm')
          ->where('shooter_id', '=', $result->shooter_id)
          ->where('event_id', '=', $event->id)
          ->find()
        : (object)array('id' => null);
    }

    View::load($this->views_directory, 'result_edit', array(
      'router'     => $this->router,
      'sids'       => $shooters_array,
      'grades'     => $grades_array,
      'result'     => $result,
      'event'      => $event,
      'match'      => $match,
      'entryForms' => $entryForms
    ));
  }

  public function edit_result_fast()
  {
    View::load($this->views_directory, 'result_edit_fast', array(
      'router' => $this->router,
      'match'  => Database::factory('Match', Arr::get($_GET, 'match_id')),
      'event'  => Database::factory('Event', Arr::get($_GET, 'event_id'))
    ));
  }

  public function edit_result_teams_fast()
  {
    $team = Database::factory('EventTeam', Arr::get($_GET, 'team_id'));
    $shootersInTeam = array();
    foreach ($team->shooters as $key => $value) {
      // echo $key;
      array_push($shootersInTeam, $key);
      //echo $key->id;
    }
    //  $shootersInTeam = $team->shooters;

    $commaSeparatedShooters = implode(",", $shootersInTeam);

    $existingResults = Database::factory('Result')
      ->where('match_id', '=', Arr::get($_GET, 'match_id'))
      ->where('shooter_id', 'in', "(" . $commaSeparatedShooters . ")")
      ->find_all();

    View::load($this->views_directory, 'result_edit_teams_fast', array(
      'router'          => $this->router,
      'match'           => Database::factory('MatchModel', Arr::get($_GET, 'match_id')),
      'event'           => Database::factory('Event', Arr::get($_GET, 'event_id')),
      'team'            => $team,
      'existingResults' => $existingResults
    ));
  }

  public function delete_result()
  {
    View::load($this->views_directory, 'result_delete', array(
      'router'   => $this->router,
      'result'   => Database::factory('Result', Arr::get($_GET, 'result_id')),
      'event_id' => Arr::get($_GET, 'event_id'),
    ));
  }

  public function publish_match()
  {
    $match = Database::factory('Match', Arr::get($_GET, 'match_id'))->find();
    if (!$match->is_loaded())
      $this->do_404();

    $match->is_published = $match->is_published ? 0 : 1;
    $match->save();

    $this->do_redirect(
      $this->router->getUrl('list-results', array('event_id' => $match->event_id, 'match_id' => $match->id))
    );
  }

  public function publish_results()
  {
    global $user_ID;
    $event = Database::factory('Event', Arr::get($_GET, 'event_id'))->find();
    if (!$event->is_loaded())
      $this->do_404();
    $pageID = wp_insert_post(array(
      'post_type'      => 'page',
      'post_content'   => '',
      'post_parent'    => 0,
      'post_author'    => $user_ID,
      'post_status'    => 'publish',
      'post_title'     => $event->name . ' Results',
      'comment_status' => 'closed',
      'ping_status'    => 'closed',
    ));
    if ($pageID === 0) {
      throw new Exception('Page Insert Failed.');
    }
    add_post_meta($pageID, 'event_id', $event->id);
    update_post_meta($pageID, '_wp_page_template', 'templates/results.php');
    $this->createPostNotificationForResults($event, $pageID);

    $topLevelMenu = wp_get_nav_menu_object('Top Level');
    wp_update_nav_menu_item($topLevelMenu->term_id, 0, array(
      'menu-item-title'     => str_replace(' ', '&nbsp;', $event->name),
      'menu-item-object'    => 'page',
      'menu-item-object-id' => $pageID,
      'menu-item-parent-id' => 16,
      'menu-item-type'      => 'post_type',
      'menu-item-status'    => 'publish',
      'menu-item-position'  => 1
    ));

    $this->do_redirect("/wp-admin/post.php?post={$pageID}&action=edit");
  }

  private function createPostNotificationForResults($event, $pageId)
  {
    $postCategory = array(wp_create_category('Event Results'));
    $postTitle    = $event->name . ' Results';
    $postName     = slugify($postTitle);
    $postContent  = '<p>The results for {{eventName}} can be found at: <a href="{{resultsUrl}}">{{postTitle}}</a></p>';
    $postContent  = str_replace("{{eventName}}", $event->name, $postContent);
    $postContent  = str_replace("{{resultsUrl}}", get_page_link($pageId), $postContent);
    $postContent  = str_replace("{{postTitle}}", $postTitle, $postContent);

    return wp_insert_post(array(
      'post_name'       => $postName,
      'post_title'      => $postTitle,
      'post_content'    => $postContent,
      'post_status'     => 'publish',
      'post_date'       => date('Y-m-d H:i:s'),
      'post_author'     => wp_get_current_user()->ID,
      'post_type'       => 'post',
      'post_category'   => $postCategory,
      'ping_status'     => 'closed',
      'comment_status'  => 'closed'
    ));
  }

  public function divisional_report()
  {
    $match_id = Arr::get('match_id');
    $match = Database::factory('Match', $match_id)->find();

    if (!$match->is_loaded()) {
      $this->do_404();
      return;
    }

    $overallHeading = "";
    if ($match->event->has_subevents) {
      if (!$match->is_aggregate) {
        $overallHeading = $match->range->subevent->name;
      } else {
        $subevent = null;
        foreach ($match->ranges as $range) {
          if ($subevent === null) {
            $subevent = $range->subevent;
          } else if ($subevent->id !== $range->subevent->id) {
            $overallHeading = $match->event->name;
            break;
          }
        }
        if ($overallHeading === "" && $subevent !== null) {
          $overallHeading = $subevent->name;
        }
      }
    }

    if ($overallHeading === "") {
      $overallHeading = $match->event->name;
    }

    View::load($this->views_directory, 'report_divisional', array(
      'overall_heading' => $overallHeading,
      'heading'     => sprintf("Match Number : %d  %s", $match->number, $match->name),
      'event_id'    => $match->event_id,
      'match'       => $match,
      'router'      => $this->router,
      'link_array'  => array("action" => "result-divisional-report", "match_id" => $match->id)
    ));
  }

  public function divisional_report_csv()
  {
    $match_id = Arr::get($_GET, 'match_id');
    $match    = Database::factory('Match', $match_id)->find();
    if (!$match->is_loaded()) {
      die('Match not found.');
    }

    $temp = tmpfile();
    if (!$temp) {
      die('Temporary file could not be created.');
    }

    $headers = 'Division,Place,LastName,PreferredName,Club,';
    if ($match->is_enter_shots)
      $headers .= 'Shots,';
    $headers .= "Score\n";
    $fileSize = strlen($headers);
    fwrite($temp, $headers);
    foreach ($match->getDivisionalReportData() as $division) {
      foreach ($division->results as $result) {
        $row = array(
          $division->short_name,
          $result->place,
          '"' . $result->shooter->details->last_name . '"',
          '"' . $result->shooter->details->preferred_name . '"',
          '"' . $result->shooter->club . '"'
        );
        if ($match->is_enter_shots)
          $row[] = $result->shots;
        $row[] = $result->__toString();
        $data  = implode(',', $row) . "\n";
        $fileSize += strlen($data);
        fwrite($temp, $data);
      }
    }
    rewind($temp);

    return array($temp, $fileSize);
  }

  public function generate_presentation_report_by_match()
  {
    $report = new \NRAA\Reports\PresentationReport();
    $template = 'report_presentation_by_match';
    $action = 'result-presentation-report-by-match';
    $this->presentation_report($report, $template, $action);
  }

  public function generate_presentation_report_by_id()
  {
    $report = new \NRAA\Reports\SidPresentationReport();
    $this->presentation_report($report, 'report_presentation_by_id', 'result-presentation-report-by-id');
  }

  public function generate_presentation_report_by_division()
  {
    $report = new \NRAA\Reports\DivisionPresentationReport();
    $this->presentation_report($report, 'report_presentation_by_division', 'result-presentation-report-by-division');
  }

  public function presentation_report($report, $template, $action) {
    if (isset($_POST['save'])) {
      $this->handle_post($_POST);
    }

    $match_id     = Arr::get('match_id');
    $subevent_id  = Arr::get('subevent_id');
    $event_id     = Arr::get('event_id');

    if ($match_id)         $report->prepareMatchData($match_id);
    else if ($subevent_id) $report->prepareSubeventData($subevent_id);
    else if ($event_id)    $report->prepareEventData($event_id);
    else
      throw new InvalidArgumentException("No match_id, subevent_id or event_id for presentation report.");

    View::load($this->views_directory, $template, array(
        'router'          => $this->router,
        'overall_heading' => $report->getHeading(),
        'data'            => $report->getData(),
        'report'          => $report,
        'link_array'      => array(
          'action'      => $action,
          'match_id'    => $match_id,
          'subevent_id' => $subevent_id,
          'event_id'    => $event_id,
        )
      )
    );
  }

  public function generate_standard_report()
  {
    $reportData = $this->gatherReportData($_GET);
    if ($reportData === null)
      $this->do_404();
    else
      View::load($this->views_directory, 'report_match', $reportData);
  }

  public function gatherReportData($options)
  {
    // WARNING: This method makes the assumption that the USER has been a good
    // little boy or girl and calculated aggregates and places before running.
    // There is currently no intelligent way for us to know whether everything
    // has been set up correctly before generating the report there is just far
    // too much stuff to go through.
    $match_id    = Arr::get($options, 'match_id');
    $event_id    = Arr::get($options, 'event_id');
    $subevent_id = Arr::get($options, 'subevent_id');
    $matches     = array();
    $link_array  = array('action' => 'result-standard-report', 'l' => Arr::get($options, 'l'));
    if ($match_id) {
      $match = Database::factory('Match', $match_id)->find();
      if (!$match->is_loaded())
        return null;

      $event_id = $match->event->id;
      $link_array['match_id'] = $match_id;
      $overall_heading = sprintf(
        __('%s - Match %s - %s'),
        $match->event->name,
        $match->number,
        $match->is_aggregate ? __('Aggregate') : $match->range->name
      );
      $matches[] = $match;
    } else if ($subevent_id) {
      $subevent = Database::factory('SubEvent', $subevent_id)->find();
      if (!$subevent->is_loaded())
        return null;

      $link_array['subevent_id'] = $subevent_id;
      $overall_heading = sprintf(
        __('%s - %s'),
        $subevent->event->name,
        $subevent->name
      );
      $event_id = $subevent->event->id;
      $matches  = $subevent->matches;
    } else if ($event_id) {
      $event = Database::factory('Event', $event_id)->find();
      if (!$event->is_loaded())
        return null;

      $link_array['event_id'] = $event_id;
      $overall_heading = $event->name;
      $matches = $event->matches;
    } else {
      return null;
    }
    if (Arr::get($options, 'is_reversed', false)) {
      usort($matches, function ($ma, $mb) {
        return $ma->number > $mb->number ? -1 : 1;
      });
    }
    foreach ($matches as $index => $match) {
      if (!$match->hasNonPlacedResults() && $match->hasResults()) {
        $heading = sprintf(
          'Match %s - %s%s',
          $match->number,
          ($match->is_aggregate
            ? __('Aggregate')
            : $match->range->name),
          ($match->name ? ' - ' . $match->name : '')
        );
        $matches[$heading] = $match;
      }
      unset($matches[$index]);
    }

    return array(
      'router'          => $this->router,
      'event_id'        => $event_id,
      'link_array'      => $link_array,
      'matches'         => $matches,
      'overall_heading' => $overall_heading
    );
  }

  public function generate_summary_team_report()
  {
    $event = Database::factory('Event', Arr::get($_GET, 'event_id'));
    $teams = Database::factory('EventTeam')
      ->where('event_id', '=', $event->id)
      ->find_all();
    $columnObjects = $event->has_subevents ? $event->subevents : $event->ranges;
    $data = array(
      'router' => $this->router,
      'link_array' => array(
        'action'   => 'result-generate-summary-team-report',
        'event_id' => $event->id,
      ),
      'event' => $event,
      'teams' => $teams,
      'columnObjects' => $columnObjects,
    );
    View::load($this->views_directory, 'report_summary_team', $data);
  }

  public function generate_detailed_team_report()
  {
    $event = Database::factory('Event', Arr::get($_GET, 'event_id'))->find();
    $teams = Database::factory('EventTeam')
      ->where('event_id', '=', $event->id)
      ->find_all();
    $data  = array(
      'router'     => $this->router,
      'link_array' => array(
        'action'   => "result-generate-detailed-team-report",
        'event_id' => $event->id,
      ),
      'event' => $event,
      'teams' => $teams
    );
    if ($event->has_subevents)
      $this->generate_detailed_team_report_subevents($data);
    $this->generate_detailed_team_report_ranges($data);
  }

  public function generate_detailed_team_report_subevents($data)
  {
    $data['columnCount'] = 1; // Start on 1 because there will always be an aggregate
    $data['subeventCount'] = 0;

    foreach ($data['event']->subevents as $subevent) {
      $subevent->colspan = count($subevent->ranges);
      $subevent->has_agg = false;
      if ($subevent->colspan > 1) { // + 1 is for the aggregate column
        $subevent->colspan += 1;
        $subevent->has_agg = true;
      }
      $data['columnCount'] += $subevent->colspan; // Add these columns to the total count
      $data['subeventCount'] += 1;
    }

    $data['teamCaptainColspan'] = floor($data['columnCount'] / 2.0);
    $data['teamCoachColspan']   = ceil($data['columnCount'] / 2.0);

    // 75% of the table will be made up of these columns
    // first 25% is the shooter name/team name column
    $data['columnWidth'] = 75 / $data['columnCount'];

    View::load($this->views_directory, 'report_detailed_team_subevents', $data);
  }

  public function generate_detailed_team_report_ranges($data)
  {
    $columnCount = count($data['event']->ranges) + 1; // + 1 is for the aggregate column
    $data['columnCount'] = $columnCount;
    // 75% of the table two columns per result
    $data['columnWidth'] = 75 / ($columnCount * 2);

    View::load($this->views_directory, 'report_detailed_team_ranges', $data);
  }

  public function generate_presentation_team_report()
  {
    $event = Database::factory('Event', Arr::get($_GET, 'event_id'))->find();
    if (!$event->is_loaded())
      die('Event not found.');

    $filePath = sprintf("%s/temp/%s.rtf", ABSPATH, sha1(rand()));

    // Build RTF file
    // TODO: Get a better way of including this.. composer should auto load it but it was not working
    require_once ABSPATH . '/wp-content/themes/nraa/vendor/phprtflite/phprtflite/lib/PHPRtfLite.php';
    PHPRtfLite::registerAutoloader();

    $rtf = new PHPRtfLite();

    // Setup fonts
    $bodyFont    = new PHPRtfLite_Font(11, 'Arial');
    $headingFont = new PHPRtfLite_Font(12, 'Arial');
    $headingFont->setBold();
    $headingFont->setUnderline();
    $headerFooterFont = new PHPRtfLite_Font(12, 'Times New Roman');

    // Paragraph formats
    $centeredParagraph = new PHPRtfLite_ParFormat(PHPRtfLite_ParFormat::TEXT_ALIGN_CENTER);
    $spacedParagraph   = new PHPRtfLite_ParFormat();
    $spacedParagraph->setSpaceBetweenLines(1);
    $spacedParagraph->setSpaceBefore(2);
    $spacedParagraph->setSpaceAfter(2);

    // Setup header/footer
    $section = $rtf->addSection();
    $header  = $section->addHeader();
    $header->writeText(__("National Rifle Association of Australia Limited"), $headerFooterFont, $centeredParagraph);
    $footer = $section->addFooter();
    $footer->writeText(sprintf(__("%s Presentation"), $event->name), $headerFooterFont, $centeredParagraph);
    $section->writeText("\n\n");

    // Sections
    foreach ($event->matches as $match) {
      $winner = $match->getTeamWinner();
      $runnerUp = $match->getTeamRunnerup();

      $section = $rtf->addSection();
      $section->setNoBreak();

      // Match heading
      $section->writeText($match . "\n", $headingFont, $spacedParagraph);

      // Winner
      if ($winner->is_loaded())
        $section->writeText(sprintf(__("Won by \t %s \t with a score of \t %s.%s\n"), $winner->name, $winner->score_whole, $winner->score_partial), $bodyFont, $spacedParagraph);
      else
        $section->writeText(__("Match places not calculated.\n"), $bodyFont, $spacedParagraph);

      // Countback
      if ($runnerUp->is_loaded()
        && $runnerUp->score_whole == $winner->score_whole
        && $runnerUp->score_partial == $winner->score_partial
      ) {
        $section->writeText(sprintf(__("Won on countback from %s\n"), $runnerUp->name), $bodyFont, $spacedParagraph);
      }
      $section->writeText("\n\n");
    }

    // Save to file
    $rtf->save($filePath);

    // Return the file
    return $filePath;
  }

  public function generate_standard_report_csv()
  {
    $match_id = Arr::get($_GET, 'match_id');
    $match    = Database::factory('Match', $match_id)->find();
    if (!$match->is_loaded())
      die('Match not found.');

    $temp = tmpfile();
    if (!$temp)
      die('Temporary file could not be created.');

    $headers = 'Discipline,Grade,Place,LastName,PreferredName,Club,';
    if ($match->is_enter_shots)
      $headers .= 'Shots,';
    $headers .= "Flags,Score\n";
    $fileSize = strlen($headers);
    fwrite($temp, $headers);
    foreach ($match->getReportData() as $discipline) {
      foreach ($discipline->grades as $grade) {
        foreach ($grade->results as $result) {
          $row = array(
            $discipline->name,
            $grade->name,
            $result->place,
            '"' . $result->shooter->details->last_name . '"',
            '"' . $result->shooter->details->preferred_name . '"',
            '"' . $result->shooter->club . '"'
          );
          if ($match->is_enter_shots)
            $row[] = $result->shots;
          $row[] = implode(" ", $result->shooter->details->flags);
          $row[] = $result->__toString();
          $data  = implode(',', $row) . "\n";
          $fileSize += strlen($data);
          fwrite($temp, $data);
        }
      }
    }
    rewind($temp);

    return array($temp, $fileSize);
  }

  public function recalculate_matches() {
    $matchIds = Arr::get('matches');
    unset($_GET['matches']);
    $nl = '<br>';

    echo 'Recalulating:' . $nl;
    var_dump($matchIds);

    foreach ($matchIds as $matchId) {
      $_GET['match_id'] = $matchId;
      $_GET['recalc'] = true;
      $_GET['is_test'] = true; //- To prevent redirect

      echo 'Starting: ' . $matchId . $nl;
      $this->calc_aggregates();
      echo $matchId . ' finished' . $nl;
    }

    echo 'Done';
  }

  public function performAutomatedCalculations($is_auto_calc_aggregates, $is_auto_calc_places, $event_id, array $matches, $allow_partial = false) {
    /* Set the limits to no-limit. */
    ini_set('memory_limit', '-1');
    set_time_limit(0);

    $done = [];
    Queue::enqueue($this->topic, ['event' => 'started']);

    $_GET['is_test']  = 1;        // set is_test to prevent redirects
    $_GET['event_id'] = $event_id;

    $agg_matches = [];
    if ($is_auto_calc_aggregates) {
      foreach ($matches as $m) {
        $match = is_string($m)
          ? Database::factory('Match', $m)->find()
          : $m;
        $agg_matches = array_merge($match->getAllRelatedAggregates(), $agg_matches);
      }
      $agg_matches = array_unique($agg_matches, SORT_REGULAR);
      foreach ($agg_matches as $agg_match) {
        $_GET['match_id'] = $agg_match->id;
        $agg_match->calculateAggregate($allow_partial);
        $this->calc_places();
        $done[] = $agg_match->id;
        Queue::enqueue($this->topic, [
          'event' => 'completed',
          'what' => 'aggregate',
          'match_id' => $agg_match->id,
          'match_number' => $agg_match->number
        ]);
      }
    }

    if ($is_auto_calc_places) {
      $needs_placement = array_udiff($matches, $agg_matches, function ($a, $b) {
        return $a->id - $b->id;
      });
      foreach ($needs_placement as $m) {
        $match = is_string($m)
          ? Database::factory('Match', $m)->find()
          : $m;
        $_GET['match_id'] = $match->id;
        $this->calc_places();
        Queue::enqueue($this->topic, [
          'event' => 'completed',
          'what' => 'placement',
          'match_id' => $match->id,
          'match_number' => $match->number
        ]);
      }
    }
    Queue::enqueue($this->topic, ['event' => 'done']);

    unset($_GET['is_test']);
    ini_set('memory_limit', '256M');
  }

  public function calc_aggregates() {
    $match_id = Arr::get($_GET, 'match_id');
    $allow_partial = Arr::get($_GET, 'allow_partial', false);

    $match = Database::factory('Match', $match_id)->find();
    if (!$match->is_loaded() || !$match->is_aggregate)
      return;

    if (!$match->calculateAggregate($allow_partial)) {
      throw new NRAAException("Unknown error occurred please contact us immediately and try to remember as much detail as you can please.");
    }

    $this->calc_places();
  }

  public function calc_places() {
    $isTest = isset($_GET['is_test']);
    $match  = Database::factory('Match', Arr::get('match_id'))->find();

    if ($match->is_loaded() && !$match->is_cancelled) {
      $event_id = Arr::get('event_id');
      $selected_event = Database::factory('Event', $event_id)->find();

      $results  = Database::factory('Result')->where('match_id', '=', $match->id);
      if (!$selected_event->is_divisional) {
        $results = $results->order_by('grade_id')
          ->order_by('score_whole', 'desc')
          ->order_by('score_partial', 'desc')
          ->find_all();
      } else {
        $results = $results
          ->order_by('score_whole', 'desc')
          ->order_by('score_partial', 'desc')
          ->find_all_divisional(true);
      }

      $isDivisional = $selected_event->is_divisional;
      $getId = function ($result) use ($isDivisional) {
        if ($isDivisional) {
          return $result->division->id;
        }
        return $result->grade_id;
      };
      $results = Result::CalculatePlaces($results, $getId);
      $groupedResults = Result::GroupResultsByIdFunc($results, $getId);
      $results = Result::UpdatePlacesWithCountBack($groupedResults, $match->getType());

      foreach ($results as $result) {
        $result->save();
      }

      if ($selected_event->is_team_event) {
        // Blow away all the previous results and rebuild
        Database::factory('TeamResult')
          ->where('match_id', '=', $match->id)
          ->delete();
        $teamResults = array();
        foreach ($selected_event->teams as $team) {
          $oldResult = $team->getResult($match->id);
          if ($oldResult === null)
            continue;
          $tr                = Database::factory('TeamResult');
          $tr->match_id      = $match->id;
          $tr->team_id       = $team->id;
          $tr->grade_id      = $oldResult->grade_id;
          $tr->place         = 0;
          $tr->shots         = $oldResult->shots;
          $tr->score_whole   = $oldResult->score_whole;
          $tr->score_partial = $oldResult->score_partial;
          $teamResults[]     = $tr;
        }
        usort($teamResults, function ($a, $b) {
          if ($a->score_whole > $b->score_whole)
            return -1;
          if ($a->score_whole < $b->score_whole)
            return 1;
          return $a->score_partial - $b->score_partial;
        });
        Result::CalculatePlaces($teamResults, $getId);
        foreach ($teamResults as $teamResult)
          $teamResult->save();
      }
    }

    if (!$isTest)
      $this->do_redirect(
        $this->router->getUrl('list-results', array('event_id' => $event_id, 'match_id' => $match->id))
      );
  }

  public function select_shooters_for_special() {
    function prepareData($data, $n) {
      $preparedData = $data;

      // Sort it by score
      usort($preparedData, function ($a, $b) {
        return $a->score->compare($b->score);
      });

      // "select" the top $n and deselect the rest
      $res = array_reduce($preparedData, function ($acc, $row) use ($n) {
        $result = $acc['result'];
        $count  = $acc['count'];
        $updated = clone $row;

        if ($count < $n) {
          $updated->selected = true;
          return array(
            'count'  => $count + 1,
            'result' => array_push($result, $updated)
          );
        }

        $updated->selected = false;
        return array(
          'count'  => $count + 1,
          'result' => array_push($result, $updated)
        );
      }, array('count' => 0, 'result' => array()));

      return $res['result'];
    }

    $event = Database::factory('Event', Arr::get($_GET, 'event_id'))->find();
    $subevent = Database::factory('SubEvent', Arr::get($_GET, 'subevent_id'))->find();
    if (!$event->is_loaded() || !$subevent->is_loaded()) {
      $this->do_404();
      return;
    }
    $user = wp_get_current_user();
    if ($user->allcaps['nraa_manage_' . strtolower($event->association->name)] != 1) {
      $this->do_403('competition');
      return;
    }

    $data = array();
    $specialRanges = Database::factory('SubEvent_Range')
      ->join('SubEvent', null, true)
      ->on('SubEvent', 'subevents.id', '=', 'subevents_ranges.subevent_id')
      ->where('subevents.special_rules', 'IS NOT', 'NULL')
      ->where('subevents.event_id', '=', $event->id)
      ->find_all_as_array_cb(function ($row) {
        return $row['range_id'];
      });
    $results = Database::factory('Result')
      ->join('EventEntryForm', null, true)
      ->on('EventEntryForm', 'event_entry_forms.shooter_id', '=', 'results.shooter_id')
      ->on('EventEntryForm', 'event_entry_forms.event_id', '=', $event->id)
      ->join('EventEntryFormSubEvent', 'LEFT OUTER', true)
      ->on('EventEntryFormSubEvent', 'event_entry_forms_subevent.event_entry_form_id', '=', 'event_entry_forms.id')
      ->on('EventEntryFormSubEvent', 'event_entry_forms_subevent.subevent_id', '=', $subevent->id)
      ->join('Shooter', null, true)
      ->on('Shooter', 'shooters.id', '=', 'results.shooter_id')
      ->join('ShooterDetails', null, true)
      ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'shooters.id')
      ->join('Grade', null, true)
      ->on('Grade', 'grades.id', '=', 'results.grade_id')
      ->join('Discipline', null, true)
      ->on('Discipline', 'disciplines.id', '=', 'grades.discipline_id')
      ->join('Match', null, true)
      ->on('Match', 'matches.id', '=', 'results.match_id')
      ->on('Match', 'matches.event_id', '=', $event->id)
      ->on('Match', 'matches.is_cancelled', '=', 0)
      ->join('Match_Range', null, true)
      ->on('Match_Range', 'match_ranges.match_id', '=', 'matches.id')
      ->where('match_ranges.range_id', 'NOT IN', Database::arrayToString($specialRanges))
      ->where('(SELECT COUNT(match_ranges.range_id) FROM match_ranges WHERE match_ranges.match_id = matches.id GROUP BY match_ranges.match_id)', '=', 1);

    switch ($subevent->special_rules) {
      case SubEvent::KALTENBERG:
        // Find only target rifle
        $results = $results->where('disciplines.name', '=', 'Target Rifle')
          ->find_all_as_array();
        // Combine the scores
        $data = array_reduce($results, function ($acc, $result) {
          $entryId = $result['EventEntryForm.id'];
          $key = $entryId . '.' . $result['Grade.id'];
          $current = Arr::get($acc, $key, (object) array(
            'entry_id'    => $entryId,
            'grade_id'    => $result['Grade.id'],
            'sid'         => $result['Shooter.sid'],
            'shooterName' => sprintf('%s %s', $result['ShooterDetails.first_name'], $result['ShooterDetails.last_name']),
            'grade'       => sprintf('%s - %s', $result['Discipline.short_name'], $result['Grade.name']),
            'selected'    => isset($result['EventEntryFormSubEvent.id']),
            'score'       => new Score()
          ));
          $current->score = $current->score->add(new Score($result['score_whole'], $result['score_partial']));
          $acc[$key] = $current;
          return $acc;
        }, $data);

        $data = prepareData($data, 20);
        break;

      case SubEvent::MACE:
        /*
        The top 4 F Target Rifle shooters shoot against each other.
        The top 4 FO Shooters shoot against each other.
        The top 4 F Standard (combined A and B), shoot against each other.
        */
        $mainDisciplines = array('F/TR', 'F Standard', 'F Open');
        $results = $results->where('disciplines.name', 'IN ' . Database::arrayToString($mainDisciplines, true), null)
          ->find_all_as_array();
        // Combine the scores
        $data = array_reduce($results, function ($acc, $result) {
          $disciplineKey = $result['Discipline.id'];
          $discipline = Arr::get($acc, $disciplineKey, array(
            'discipline_id' => $disciplineKey,
            'discipline'    => $result['Discipline.name'],
            'data'          => array()
          ));
          $shooterKey = $result['EventEntryForm.id'];
          $shooter = Arr::get($discipline['data'], $shooterKey, (object) array(
            'entry_id'    => $shooterKey,
            'grade_id'    => $result['Grade.id'],
            'sid'         => $result['Shooter.sid'],
            'shooterName' => sprintf('%s %s', $result['ShooterDetails.first_name'], $result['ShooterDetails.last_name']),
            'grade'       => $result['Grade.name'],
            'selected'    => isset($result['EventEntryFormSubEvent.id']),
            'score'       => new Score()
          ));
          $shooter->score = $shooter->score->add(new Score($result['score_whole'], $result['score_partial']));
          $discipline['data'][$shooterKey] = $shooter;
          $acc[$disciplineKey] = $discipline;
          return $acc;
        }, $data);
        foreach ($data as &$discipline) {
          $discipline['data'] = prepareData($discipline['data'], 4);
        }
        usort($data, function ($a, $b) {
          return (
            $a['discipline_id'] === $b['discipline_id'] ? 0
              : ($a['discipline_id'] < $b['discipline_id'] ? -1 : 1));
        });
        break;

      default:
        $this->do_404();
        return;
    }

    View::load($this->views_directory, 'select_shooters_for_special', array(
      'viewsDirectory' => $this->views_directory,
      'router'    => $this->router,
      'backUrl'   => $this->router->getUrl('select-match', array('event_id' => $event->id)),
      'event'     => $event,
      'subevent'  => $subevent,
      'data'      => $data
    ));
  }

  public function widget_callback() {
    $gradeToLimit = array(
      0 => 3,
      1 => 7,
      2 => 5
    );
    $disciplines = Database::factory('Discipline')->find_all();
    foreach ($disciplines as $discipline) {
      foreach ($discipline->grades as $grade) {
        $grade->shooters = Database::factory('Shooter')
          ->join('ShooterDetails')
          ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'shooters.id')
          ->join('ShooterCalculatedGrade')
          ->on('ShooterCalculatedGrade', 'shooter_calculated_grades.shooter_id', '=', 'shooters.id')
          ->where('shooter_calculated_grades.grade_id', '=', $grade->id)
          ->where('shooter_calculated_grades.number_of_shoots', '>', 5)
          ->order_by('avg_score', 'desc')
          ->limit(isset($gradeToLimit[$grade->id]) ? $gradeToLimit[$grade->id] : $gradeToLimit[0])
          ->find_all();
      }
    }
    View::load($this->views_directory, 'leaderboard', array(
      'router' => $this->router,
      'disciplines' => $disciplines
    ));
  }

  public function callback() {
    echo $this->router->execute();
  }

  public function init() {
    if (!current_user_can(Module::CAPABILITY_RESULT)) {
      return;
    }

    $this_obj =& $this;
    $page_slug = basename(__FILE__);

    add_action("admin_menu", function () use ($this_obj, $page_slug) {
      $page = add_submenu_page('EventManagementModule.php', __('Result Management'), __('Results'), Module::CAPABILITY_RESULT, $page_slug, array($this_obj, 'callback'));
      add_action('admin_print_styles-' . $page, function () {
        wp_enqueue_script('jquery-migrate');
        wp_enqueue_script('jquery-ui-cdn');
        wp_enqueue_script('jquery-validate');
        wp_enqueue_script('chosen');
        wp_enqueue_script('nraa-admin-result', get_stylesheet_directory_uri() . '/js/dist/admin-result.min.js', array('jquery-cdn', 'nraa-admin-scripts'), VERSION, true);
        wp_enqueue_style('jquery-ui-styles');
        wp_enqueue_style('chosen');
        if (Arr::get($_GET, 'action') === 'edit-result-teams-fast' || Arr::get($_GET, 'action') === 'edit-result-fast') {
          wp_enqueue_script('underscorejs');
          wp_enqueue_script('nraa-admin-result-fast', get_stylesheet_directory_uri() . '/js/dist/admin-result-fast.min.js', array('jquery-cdn', 'knockoutjs', 'nraa-admin-result'), VERSION, true);
          wp_enqueue_style('nraa-admin-result-fast-styles', get_stylesheet_directory_uri() . '/css/dist/admin-result-fast.min.css', array('nraa-admin-styles'), VERSION, 'all');
        }
        if (strpos(Arr::get('action'), '-report') !== false) {
          wp_enqueue_style('nraa-report-styles');
        }
      });
    });
    add_action('wp_dashboard_setup', function () use($this_obj) {
      wp_add_dashboard_widget('results_widget', __('Results Leaderboards'), array($this_obj, 'widget_callback'));
    });
    add_action("wp_before_admin_bar_render", function () use ($page_slug) {
      NraaMenuBar::getInstance()->add_sub_menu('Results', get_admin_url(null, 'admin.php?page=' . $page_slug), SHORTNAME . '_management');
    });
  }
}
