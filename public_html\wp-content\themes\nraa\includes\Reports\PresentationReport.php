<?php
/********************************************************************************
 * Presentation Report Class                                                    *
 *                                                                              *
 * Collects data required for the presentation report and exposes the required  *
 * information on public functions.                                             *
 *                                                                              *
 * @version 1.0                                                                 *
 * @package NRAA                                                                *
 * @subpackage Reports                                                          *
 * <AUTHOR> Group                                                         *
 ********************************************************************************/
namespace NRAA\Reports;

use Database;
use Event;
use EventPresentationOrdering;
use Subevent;
use Match;
use NRAA\Exceptions\PresentationReportException;
use NRAA\Exceptions\PresentationReportInvalidArgumentException;

class PresentationReport
{
  protected $heading;
  protected $data;
  protected $isPrepared;
  protected $customOrder;

  /**
   * Constructor - Initializes member variables
   */
  public function __construct() {
    $this->heading = "";
    $this->data = array();
    $this->isPrepared = false;
    $this->customOrder = null;
  }

  /**
   * Prepare data for a specific match only.
   *
   * @param int $match_id
   * @throws PresentationReportInvalidArgumentException
   */
  public function prepareMatchData($match_id) {
    $match = new MatchModel($match_id);
    $match->find();

    if (!$match->is_loaded())
      throw new PresentationReportInvalidArgumentException("match_id was not for a valid match");

    $matchName = null;
    if ($match->is_aggregate) $matchName = __('Aggregate');
    else if ($match->name) $matchName = $match->name;
    else $matchName = $match->range->name;

    $this->setHeading(
      sprintf(
        __('Presentation Report - %s - Match %s - %s'),
        $match->event->name,
        $match->number,
        $matchName
      )
    );
    $this->addMatch($match);
    $this->isPrepared = true;
  }

  /**
   * Prepare data for a subevent.
   *
   * @param int $subevent_id
   * @throws PresentationReportInvalidArgumentException
   */
  public function prepareSubeventData($subevent_id) {
    $subevent = new Subevent($subevent_id);
    $subevent->find();

    if (!$subevent->is_loaded())
      throw new PresentationReportInvalidArgumentException("subevent_id was not for a valid subevent");

    $this->setHeading(
      sprintf(
        'Presentation Report - %s - %s',
        $subevent->event->name,
        $subevent->name
      )
    );
    $this->addMatches($subevent->matches);
    $this->sortData();
    $this->isPrepared = true;
  }

  /**
   * Prepare data for an event.
   *
   * @param int $event_id
   * @throws PresentationReportInvalidArgumentException
   */
  public function prepareEventData($event_id) {
    $event = new Event($event_id);
    $event->find();

    if (!$event->is_loaded())
      throw new PresentationReportInvalidArgumentException("event_id was not for a valid event");

    $this->setHeading('Presentation Report - ' . $event->name);
    $this->addMatches($event->matches);
    $this->sortData();
    $this->isPrepared = true;
  }

  /**
   * Get the overall heading.
   */
  public function getHeading() {
    if ($this->heading === "")
      throw new PresentationReportException("Heading has not yet been prepared");

    return $this->heading;
  }

  /**
   * Get the prepared data.
   */
  public function getData() {
    if (!$this->isPrepared)
      throw new PresentationReportException("Data has not yet been prepared");
    if (count($this->data) === 0) {
      throw new PresentationReportException("Data is empty. Please check that you have configured badges and calculated aggregates correctly.");
    }

    return $this->data;
  }

  /**
   * Set the heading.
   *
   * @param string $heading
   * @throws PresentationReportInvalidArgumentException
   */
  protected function setHeading($heading) {
    if (!is_string($heading))
      throw new PresentationReportInvalidArgumentException("setHeading expects a string");

    $this->heading = $heading;
  }

  /**
   * Add an array of \MatchModel objects.
   *
   * @param array $matches
   * @throws PresentationReportInvalidArgumentException
   */
  protected function addMatches($matches) {
    if (!is_array($matches))
      throw new PresentationReportInvalidArgumentException("setMatches expects an array");

    foreach ($matches as $match) {
      $this->addMatch($match);
    }
  }

  /**
   * Add a match and prepare the data for this match.
   *
   * @param Match $match
   * @throws PresentationReportInvalidArgumentException
   */
  protected function addMatch($match) {
    if (get_class($match) !== 'Match')
      throw new PresentationReportInvalidArgumentException("addMatch expects a match object");

    $data = new \stdClass();

    $data->number = $match->number;
    $data->heading = sprintf(
      'Match %d - %s',
      $match->number,
      $match->getComplexName()
    );
    $data->grades = array();
    if ($match->is_cancelled) {
      $data->heading .= ' - Cancelled';
      return;
    }

    foreach ($match->badges as $badge) {
      if ($badge->grade_id === null) {
        continue;
      }
      $grade = new \stdClass();
      $grade->grade_id = $badge->grade_id;

      $grade->heading = sprintf(
        '%s - %s',
        $badge->grade->discipline->name,
        $badge->grade->name
      );

      $results = \Database::factory('Result')
        ->where('place', '<=', $badge->number_of_badges)
        ->where('grade_id', '=', $badge->grade_id)
        ->where('match_id', '=', $match->id)
        ->limit($badge->number_of_badges)
        ->order_by('place', 'ASC')
        ->find_all();

      if (count($results) === 0)
        continue;

      $grade->rows = array();
      foreach ($results as $result) {
        $row = new \stdClass();

        $row->place         = $result->place;
        $row->shooter       = $result->shooter;
        $row->shooterFlags  = $result->shooterInfo;
        $row->score         = $result->score;

        $grade->rows[] = $row;
      }

      $data->grades[$badge->grade_id] = $grade;
    }

    if ($data->grades) {
      $this->customOrdering($match->event_id, $data->grades);
      $this->data[] = $data;
    }
  }

  protected function customOrdering($event_id, &$dataToSort) {
    if ($this->customOrder === null) {
      $this->customOrder = Database::factory('EventPresentationOrdering')
        ->where('event_id', '=', $event_id)
        ->find_all_as_array('order', true);
    }
    if (is_array($this->customOrder)) {
      $co = $this->customOrder;
      usort($dataToSort, function ($a, $b) use (&$co) {
        if (!isset($co[$a->grade_id]) || !isset($co[$b->grade_id]))
          return 1;
        return $co[$a->grade_id] > $co[$b->grade_id] ? 1 : -1;
      });
    }
  }

  /**
   * Sort the data by Match number.
   */
  protected function sortData() {
    usort($this->data, function($a, $b) {
      return $a->number > $b->number ? 1 : -1;
    });
  }
}
