<?php defined('ABSPATH') or die('No direct script access.');

/**
 * Module - SID Management (Formerly UIN management)
 *
 * NRAA requested UIN (Unique Identification Number) to be renamed to SID (Shooter ID)
 */
class ShooterManagementModule extends Module implements IModule
{
  public $router;
  protected $viewsDirectory;

  public function __construct($skip_roles = false)
  {
    parent::__construct($skip_roles);

    $this->router = new SimpleRouter(array(
      'base_url' => 'admin.php?page=' . basename(__FILE__)
    ));

    $this->viewsDirectory = __DIR__ . '/views/';

    $this->router->setRoute('', array($this, 'index'))
      ->setRoute('edit-shooter', array($this, 'edit_shooter'))
      ->setRoute('create_note', array($this, 'create_note'))
      ->setRoute('edit-note', array($this, 'edit_note'))
      ->setRoute('delete-note', array($this, 'delete_note'))
      ->setRoute('view-grading-details', array($this, 'view_grading_details'))
      ->setRoute('view-audit-changes', array($this, 'view_audit_changes'))
      ->setRoute('view-change', array($this, 'view_change'))
      ->setRoute('edit-secondary-membership', array($this, 'edit_secondary_membership'))
      ->setRoute('delete-secondary-membership', array($this, 'delete_secondary_membership'))
      ->setRoute('create-secondary-membership', array($this, 'create_secondary_membership'))
      ->setRoute('delete-shooter', array($this, 'delete_shooter'));
  }

  public function index()
  {
    if (isset($_POST) && $this->handlePostBack() === false)
      return;

    $pagination = new Pagination(
      Database::factory('Shooter')
        ->join('ShooterDetails', 'LEFT')
        ->on('ShooterDetails', 'shooter_id', '=', 'id'),
      $this->router->getUrl('')
    );
    $pagination->setSearchFields(array(
      'sid', 'email', 'preferred_name', 'first_name',
      'last_name', 'date_of_birth', 'home_phone', 'mobile_phone'
    ));
    $shooters = $pagination->getData();
    View::load($this->viewsDirectory, 'read', array(
      'shooters' => $shooters,
      'router' => $this->router,
      'pagination' => $pagination
    ));
  }

  protected function handlePostBack()
  {
    if (!isset($_POST))
      return true;

    if (isset($_POST['deleteShooter'])) {
      $deleted = Database::factory('Shooter', $_POST['id'])
        ->delete();

      $msg = isset($deleted->id)
        ? 'Delete was unsuccessful'
        : 'SID deleted';

      add_message($msg);

      return true;
    } else if (isset($_POST['save'])) {
      $type = $_POST['save'];
      unset($_POST['save']);

      switch ($type) {
        case 'sid':
          $shooter = Database::factory('Shooter', $_POST['id'])->find();
          $association = Database::factory('Association', $_POST['membership']['association_id'])->find();

          if (!$_POST['id']) {
            if ($association->is_loaded())
              $shooter->sid = $association->getNextSid();
            else {
              $this->do_404();
              return false;
            }
          }

          $shooterValues = array_intersect_key($_POST, array_flip(array('sid', 'club', 'old_uin', 'status')));
          $shooter->values($shooterValues)
            ->save();

          if (isset($_POST['details'])) {
            $_POST['details']['date_of_birth'] = strtotime($_POST['details']['date_of_birth']);

            $shooterDetails = Database::factory('ShooterDetails', $shooter->id)->find();
            if (!$shooterDetails->is_loaded())
              $shooterDetails->shooter_id = $shooter->id;

            // flags
            $flags = array('is_veteran', 'is_super_veteran', 'is_lady', 'is_u25', 'is_junior', 'is_tyro', 'is_service_person', 'is_police_person');
            foreach ($_POST['details'] as $detail => $value) {
              if (in_array($detail, $flags)) {
                if ($value == 1)
                  $shooterDetails->setFlag($detail);
                else if ($shooterDetails->hasFlag($detail))
                  $shooterDetails->unsetFlag($detail);
                unset($_POST['details'][$detail]);
              }
            }

            $shooterDetails
              ->values($_POST['details'])
              ->save();

            unset($_POST['details']);
          }

          if (isset($_POST['membership'])) {
            // TODO: Update membership
            $shooterPrimaryMembership = Database::factory('ShooterMembership', $shooter->id)->find();
            if (!$shooterPrimaryMembership->is_loaded())
              $shooterPrimaryMembership->shooter_id = $shooter->id;

            if ($_POST['membership']['joined_date'])
              $_POST['membership']['joined_date'] = strtotime($_POST['membership']['joined_date']);

            if ($_POST['membership']['expiry_date'])
              $_POST['membership']['expiry_date'] = strtotime($_POST['membership']['expiry_date']);

            if ($_POST['membership']['license_expiry_date'])
              $_POST['membership']['license_expiry_date'] = strtotime($_POST['membership']['license_expiry_date']);

            $shooterPrimaryMembership
              ->values($_POST['membership'])
              ->save();

            unset($_POST['membership']);
          }

          if (isset($_POST['addresses'])) {
            // FORMAT addresses[{$address->id}][type]
            $residentialAddress = $_POST['addresses']['primaryAddress'];
            $residentialAddressRecord = Database::factory('ShooterAddress', $residentialAddress['id'])
              ->find();

            if (!$residentialAddressRecord->is_loaded())
              $residentialAddressRecord->shooter_id = $shooter->id;

            $residentialAddressRecord->type = 'primary';
            $residentialAddressRecord
              ->values($residentialAddress)
              ->save();

            $mailingAddress = $_POST['addresses']['mailingAddress'];
            $mailingAddressRecord = Database::factory('ShooterAddress', $mailingAddress['id'])
              ->find();

            if (!$mailingAddressRecord->is_loaded())
              $mailingAddressRecord->shooter_id = $shooter->id;

            $mailingAddressRecord->type = "mailing";
            $mailingAddressRecord->values($mailingAddress)
              ->save();

            unset($_POST['addresses']);
          }

          if (isset($_POST['accreditation'])) {

            $rangeOfficer = $_POST['accreditation']['rangeOfficer'];
            $rangeOfficerAccreditation = Database::factory('ShooterAccreditation', $rangeOfficer['id'])
              ->find();

            if ($rangeOfficerAccreditation->is_loaded()) {
              if (isset($rangeOfficer['isRangeOfficer']) && $rangeOfficer['isRangeOfficer'] == 1) {
                $rangeOfficerAccreditation->expiry_date = strtotime($rangeOfficer['expiry_date']);
                $rangeOfficerAccreditation->save();
              } else {
                $rangeOfficerAccreditation->delete();
              }
            } else if (isset($rangeOfficer['isRangeOfficer']) && $rangeOfficer['isRangeOfficer'] == 1) {
              $rangeOfficerAccreditation->expiry_date = strtotime($rangeOfficer['expiry_date']);
              $rangeOfficerAccreditation->type = 'Range';
              $rangeOfficerAccreditation->shooter_id = $shooter->id;

              $rangeOfficerAccreditation->save();
            }

            $buttsOfficer = $_POST['accreditation']['buttsOfficer'];
            $buttsOfficerAccreditation = Database::factory('ShooterAccreditation', $buttsOfficer['id'])
              ->find();

            if ($buttsOfficerAccreditation->is_loaded()) {
              if (isset($buttsOfficer['isButtsOfficer']) && $buttsOfficer['isButtsOfficer'] == 1) {
                $buttsOfficerAccreditation->expiry_date = strtotime($buttsOfficer['expiry_date']);
                $buttsOfficerAccreditation->save();
              } else {
                $buttsOfficerAccreditation->delete();
              }
            } else if (isset($buttsOfficer['isButtsOfficer']) && $buttsOfficer['isButtsOfficer'] == 1) {
              $buttsOfficerAccreditation->expiry_date = strtotime($buttsOfficer['expiry_date']);
              $buttsOfficerAccreditation->type = 'Butts';
              $buttsOfficerAccreditation->shooter_id = $shooter->id;
              $buttsOfficerAccreditation->save();
            }
            unset($_POST['accreditation']);
          }

          add_message('SID saved');
          $this->do_redirect($this->router->getUrl('edit-shooter', array('id' => $shooter->id)));
          break;

        case 'note':
          $current_user = wp_get_current_user();
          $_POST['date'] = time();
          $_POST['by'] = $current_user->display_name;

          Database::factory('ShooterNote', $_POST['id'])
            ->find()
            ->values($_POST)
            ->save();

          add_message('Note saved');
          $this->do_redirect($this->router->getUrl('edit-shooter', array('id' => $_POST['shooter_id'])));
          break;

        case 'secondary_membership':
          $_POST['expiry_date'] = strtotime($_POST['expiry_date']);
          $_POST['joined_date'] = strtotime($_POST['joined_date']);

          Database::factory('ShooterSecondaryMembership', $_POST['id'])
            ->find()
            ->values($_POST)
            ->save();

          $this->do_redirect($this->router->getUrl('edit-shooter', array('id' => $_POST['shooter_id'])));
      }
    }
  }

  public function view_grading_details()
  {
    $shooter = Database::factory('Shooter', Arr::get($_GET, 'shooter_id'))->find();
    $discipline = Database::factory('Discipline', Arr::get($_GET, 'discipline_id'))->find();

    if (!$discipline->is_loaded() || !$shooter->is_loaded()) {
      View::load($this->common_templates_dir, '404');

      return;
    }

    $results = Database::factory('Result')
      ->join('MatchModel', null, true)
      ->on('MatchModel', 'results.match_id', '=', 'matches.id')
      ->join('Grade', null, true)
      ->on('Grade', 'results.grade_id', '=', 'grades.id')
      ->join('Event', null, true)
      ->on('Event', 'matches.event_id', '=', 'events.id')
      ->where('results.shooter_id', '=', $shooter->id)
      ->where('grades.discipline_id', '=', $discipline->id)
      ->where('matches.is_graded', '=', 1)
      ->where('matches.is_cancelled', '=', 0)
      ->where('events.end_date', '>=', strtotime(GRADING_START_DATE))
      ->where('events.is_team_event', '=', 0)
      ->order_by('events.end_date', 'DESC')
      ->order_by('matches.number', 'DESC')
      ->find_all();

    foreach ($results as $result) {
      $highScore = $result->match->getHighScoreResult($result->grade->discipline_id);
      $result->match->highScore = $highScore;
      $result->scorePercentage = $result->score->percentageOf($highScore->score);
    }

    // Flag the ones used in the grading algorithm
    $numberOfShoots = 0;
    foreach ($results as $result) {
      $result->flagged = true;
      if (++$numberOfShoots >= MAX_SHOOTS)
        break;
    }

    $calculatedGrades = Database::factory('ShooterCalculatedGrade')
      ->join('Grade', null, true)
      ->on('Grade', 'grades.id', '=', 'shooter_calculated_grades.grade_id')
      ->where('shooter_id', '=', $shooter->id)
      ->where('grades.discipline_id', '=', $discipline->id)
      ->find_all();

    View::load($this->viewsDirectory, 'grade_details', array(
      'shooter' => $shooter,
      'discipline' => $discipline,
      'results' => $results,
      'calculatedGrades' => $calculatedGrades,
      'router' => $this->router
    ));
  }

  public function view_audit_changes()
  {
    $shooter = Database::factory('Shooter', Arr::get($_GET, 'shooter_id'))->find();
    View::load($this->viewsDirectory, 'audit_view', array(
      'shooter' => $shooter,
      'router'  => $this->router
    ));
  }

  public function edit_shooter()
  {
    $grades = Database::factory('Grade')->find_all();
    $disciplines = Database::factory('Discipline')->find_all();
    $associations = Database::factory('Association')->find_all_as_array('name', true);
    $shooter = Database::factory('Shooter', Arr::get($_GET, 'id'));

    View::load($this->viewsDirectory, 'create_update', array(
      'router' => $this->router,
      'shooter' => $shooter,
      'associations' => $associations,
      'grades' => $grades,
      'disciplines' => $disciplines
    ));
  }

  public function create_note()
  {
    View::load($this->viewsDirectory, 'create_update_note', array(
      'router' => $this->router,
      'shooterId' => $_GET['shooterId']
    ));
  }

  public function edit_note()
  {
    $note = (isset($_GET['id'])) ? Database::factory('ShooterNote', $_GET['id']) : null;

    View::load($this->viewsDirectory, 'create_update_note', array(
      'router' => $this->router,
      'note' => $note,
      'shooterId' => $note->shooter_id
    ));
  }

  public function delete_note()
  {
    $noteId = Arr::get($_GET, 'id');
    $note = Database::factory('ShooterNote', $noteId)->find();
    if (!$note->is_loaded()) {
      $this->do_404();
      return;
    }

    $shooterId = $note->shooter_id;
    $note->delete();

    $this->do_redirect($this->router->getUrl('edit-shooter', array('id' => $shooterId)));
  }

  public function edit_secondary_membership()
  {
    $secondaryMembership = (isset($_GET['id'])) ? Database::factory('ShooterSecondaryMembership', $_GET['id']) : null;

    $associations = Database::factory('Association')->find_all_as_array('name', true);
    View::load($this->viewsDirectory, 'create_update_membership', array(
      'router' => $this->router,
      'shooterId' => $_GET['shooterId'],
      'associations' => $associations,
      'membership' => $secondaryMembership
    ));
  }

  public function delete_secondary_membership()
  {
    $secondaryMembership = (isset($_GET['id'])) ? Database::factory('ShooterSecondaryMembership', $_GET['id']) : null;

    $shooterId = $secondaryMembership->shooter_id;
    $secondaryMembership->delete();

    $shooter = Database::factory('Shooter', $shooterId);
    $associations = Database::factory('Association')->find_all_as_array('name', true);
    View::load($this->viewsDirectory, 'create_update', array(
      'router' => $this->router,
      'shooter' => $shooter,
      'associations' => $associations
    ));
  }

  public function create_secondary_membership()
  {
    //$secondaryMembership = ( isset( $_GET['id'] ) ) ? Database::factory( 'ShooterSecondaryMembership', $_GET['id'] ) : NULL;
    $associations = Database::factory('Association')->find_all_as_array('name', true);
    View::load($this->viewsDirectory, 'create_update_membership', array(
      'router' => $this->router,
      'shooterId' => $_GET['shooterId'],
      'associations' => $associations,
    ));
  }

  public function delete_shooter()
  {
    View::load($this->viewsDirectory, 'delete', array(
      'router' => $this->router,
      'sid' => Database::factory('Shooter', $_GET['id'])
    ));
  }
  
  public function view_change()
  {
    View::load($this->viewsDirectory, 'view_change', array(
      'router' => $this->router,
      'shooterId' => Arr::get('shooter_id'),
      'audit' => Database::factory('AuditTrail', Arr::get('audit_id'))
    ));
  }

  /********************************************* START REQUESTS *******************************************************/
  public function requests_callback()
  {
    $this->router = new SimpleRouter(array(
      'base_url' => 'admin.php?page=Requests' . basename(__FILE__)
    ));

    echo $this->router->setRoute('', array($this, 'requests_list'))
      ->setRoute('view', array($this, 'requests_view'))
      ->execute();
  }

  public function requests_list()
  {
    if (isset($_POST))
      $this->requests_handlePostBack();

    $pagination = new Pagination(Database::factory('ShooterUpdateRequest'), $this->router->getUrl(''));
    $pagination->setSearchFields(array(
      'sid', 'email', 'first_name', 'last_name', 'preferred_name'
    ));
    $requests = $pagination->getData();

    View::load($this->viewsDirectory, 'requests_list', array(
      'pagination' => $pagination,
      'router' => $this->router,
      'requests' => $requests
    ));
  }

  protected function requests_handlePostBack()
  {
    $sid = Arr::get($_POST, 'sid');
    $id = Arr::get($_POST, 'id');
    if (isset($_POST['delete'])) {
      Database::factory('ShooterUpdateRequest', $id)->delete();
      add_message('SID request deleted');
    } else if (isset($_POST['accept'])) {
      unset($_POST['id']);
      unset($_POST['accept']);

      $request = Database::factory('ShooterUpdateRequest', $id);
      if ($request->type == 'change') {
        $shooter = Database::factory('Shooter')->where('sid', '=', $sid)->find();
        if ($shooter->is_loaded()) {
          $shooter->values($_POST)->save();
          $request->delete();
          add_message('SID information updated');
        } else {
          add_message("Could not find SID ({$_POST['sid']}) in the SID database");
        }
      } else if ($request->type == 'new') {
        Database::factory('Shooter')
          ->values($_POST)
          ->save();
        $request->delete();
        add_message('New SID record created');
      }
    }
  }

  public function requests_view()
  {
    $request = isset($_GET['id']) ? Database::factory('ShooterUpdateRequest', $_GET['id']) : null;
    View::load($this->viewsDirectory, 'request_view', array(
      'router' => $this->router,
      'request' => $request
    ));
  }
  /********************************************** END REQUESTS ********************************************************/

  /********************************************* START MERGE SID ******************************************************/
  public function mergesid_callback()
  {
    $this->router = new SimpleRouter(array(
      'base_url' => 'admin.php?page=MergeSIDs'
    ));
    $this->router->setRoute('', array($this, 'mergesid'));

    echo $this->router->execute();
  }

  public function mergesid()
  {
    if (isset($_POST)) {
      $this->mergesid_handlePostBack();
    }

    $shootersArray = Database::factory('Shooter')
      ->join('ShooterDetails')
      ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'shooters.id')
      ->find_all_as_array_cb(function ($shooter) {
        return array(
          'id' => $shooter['id'],
          'title' => sprintf(
            '%s - %s %s - %s',
            $shooter['sid'] ? $shooter['sid'] : $shooter['id'],
            $shooter['preferred_name'] ? $shooter['preferred_name'] : $shooter['first_name'],
            $shooter['last_name'],
            substr($shooter['club'], 0, 8)
          )
        );
      });
    array_unshift($shootersArray, array('id' => '', 'title' => '--- Select SID ---'));

    View::load($this->viewsDirectory, 'mergesid', array(
      'router' => $this->router,
      'sids' => $shootersArray
    ));
  }

  protected function mergesid_handlePostBack()
  {
    $fromShooterId = Arr::get($_POST, 'from-shooter-id', false);
    $toShooterId = Arr::get($_POST, 'to-shooter-id', false);
    if ($fromShooterId !== false && $toShooterId !== false) {
      $fromShooter = Database::factory('Shooter', $fromShooterId)->find();
      if (!$fromShooter->is_loaded()) {
        add_message("From SID was not able to be loaded.");
        return;
      }
      $toShooter = Database::factory('Shooter', $toShooterId)->find();
      if (!$toShooter->is_loaded()) {
        add_message("To SID was not able to be loaded.");
        return;
      }

      $fromShooter->mergeInto($toShooter);
      add_message("SIDs have been merged successfully.");
    }
  }
  /********************************************** END MERGE SID *******************************************************/

  public function callback()
  {
    echo $this->router->execute();
  }

  public function widget_callback()
  {
    View::load($this->viewsDirectory, 'widget', array(
      //'' => Database::factory( 'ShooterUpdateRequest' )->where('type', '=', 'new')->count_all(),
      //'' => Database::factory( 'ShooterUpdateRequest' )->where('type', '=', 'change')->count_all(),
      'shooterCount' => Database::factory('Shooter')->count_all()
    ));
  }

  public function importCsv($csv, $options = null)
  {
    if ($options === null) {
      $options = array(
        'sid' => 1,
        'club' => 1,
        'old_uin' => 1,
        'status' => 1,
        'email' => 1,
        'title' => 1,
        'first_name' => 1,
        'middle_name' => 1,
        'last_name' => 1,
        'preferred_name' => 1,
        'gender' => 1,
        'date_of_birth' => 1,
        'home_phone' => 1,
        'mobile_phone' => 1,
        'is_right_handed' => 1,
        'is_using_bench' => 1,
        'is_coach' => 1,
        'is_competition_coach' => 1,
        'association' => 1,
        'membership_no' => 1,
        'joined_date' => 1,
        'expiry_date' => 1,
        'license_number' => 1,
        'license_expiry_date' => 1,
        'has_atr_magazine' => 1,
        'notes' => 1,
        'line_1' => 1,
        'line_2' => 1,
        'line_3' => 1,
        'suburb' => 1,
        'state' => 1,
        'postcode' => 1,
        'country' => 1
      );
    }
    $shooters = array();
    $csv = preg_replace('~\R~u', ",", $csv);
    $data = array_chunk(str_getcsv($csv), count($options));
    array_shift($data); //- extract headings
    $columns = array_keys($options);
    foreach ($data as $row) {
      if (count($row) != count($options))
        continue;
      $shooter = Database::factory('Shooter');
      foreach ($row as $index => $value) {
        if ($value == 'null')
          continue;

        $column = $columns[$index];
        switch ($column) {
          case 'association':
            $association = Database::factory('Association')
              ->where('name', '=', strtoupper($value))
              ->find();
            $value = $association->id;
            $column = 'association_id';
            break;
        }

        if (strpos($column, 'date') !== false) {
          $dateArr = explode('/', $value);
          if (strlen($dateArr[2]) < 4) {
            $value = sprintf(
              '%s/%s/%s%s',
              $dateArr[0],
              $dateArr[1],
              intval($dateArr[2]) < intval(date('y')) ? '20' : '19',
              $dateArr[2]);
          }
          $date = DateTime::createFromFormat('H:i:s j/m/Y', '00:00:00 ' . $value);
          $value = $date->getTimestamp();
        }

        $shooter->$column = trim($value);
      }
      $shooters[] = $shooter;
    }

    return $shooters;
  }

  public function init()
  {
    if (!current_user_can(Module::CAPABILITY_SHOOTER)) {
      return;
    }
    $this_obj =& $this;
    $page_slug = basename(__FILE__);
    add_action('admin_menu', function () use ($this_obj, $page_slug) {
      $page = add_menu_page(__('Shooter Management'), __('Shooters'), Module::CAPABILITY_SHOOTER, $page_slug, array($this_obj, 'callback'), 'div');
      $requestFormsPage = add_submenu_page($page_slug, __('Shooter Request Form Management'), __('Request Forms'), Module::CAPABILITY_SHOOTER, 'Requests' . $page_slug, array($this_obj, 'requests_callback'));
      $mergeSidPage = add_submenu_page($page_slug, __('Merge SIDs'), __('Merge SIDs'), Module::CAPABILITY_SHOOTER, 'MergeSIDs', array($this_obj, 'mergesid_callback'));
      $styles_func = function () {
        wp_enqueue_script('jquery-cdn');
        wp_enqueue_script('jquery-migrate');
        wp_enqueue_script('jquery-ui-cdn');
        wp_enqueue_script('jquery-validate');
        wp_enqueue_script('nraa-admin-scripts');
        wp_enqueue_style('jquery-ui-styles');

        if (Arr::get('page') === 'MergeSIDs') { // FIXME: I'm sure there's a wordpressy way to do this but >_> <_<
          wp_enqueue_script('chosen');
          wp_enqueue_style('chosen');
          wp_enqueue_script('nraa-mergesid', get_stylesheet_directory_uri() . '/js/dist/mergesid.min.js', array('jquery-cdn', 'chosen'), VERSION, true);
          wp_enqueue_script('twitter-bootstrap-cdn');
          wp_enqueue_style('twitter-bootstrap-cdn');
        }
      };
      add_action('admin_print_styles-' . $page, $styles_func);
      add_action('admin_print_styles-' . $requestFormsPage, $styles_func);
      add_action('admin_print_styles-' . $mergeSidPage, $styles_func);
    });
    add_action('wp_dashboard_setup', function () use ($this_obj) {
      wp_add_dashboard_widget('sid_management_widget', __('Shooter Management'), array($this_obj, 'widget_callback'));
    });
    add_action('wp_before_admin_bar_render', function () use ($page_slug) {
      $name = SHORTNAME . '_management';
      NraaMenuBar::getInstance()
        ->replace_href_for_menu_on_post('edit', 'nraa-shooter-list', get_admin_url(null, 'admin.php?page=' . $page_slug))
        ->add_sub_menu('Shooters', get_admin_url(null, 'admin.php?page=' . $page_slug), $name)
        ->add_sub_menu('Request Forms', get_admin_url(null, 'admin.php?page=Requests' . $page_slug), $name)
        ->add_sub_menu('Merge SIDs', get_admin_url(null, 'admin.php?page=MergeSIDs'), $name);
    });
  }
}
