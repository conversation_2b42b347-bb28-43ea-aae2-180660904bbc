<?php defined('ABSPATH') or die('No direct script access.');

/**
 * Model for NRAA Event Results
 *
 * <AUTHOR> Group Pty Ltd
 */
class Result extends Database
{
  protected $_tablename = "results";
  protected $_belongs_to = array(
    'match' => array(
      'model' => 'MatchModel',
      'foreign_key' => 'match_id',
    ),
    'shooter' => array(
      'model' => 'Shooter',
      'foreign_key' => 'shooter_id',
    ),
    'grade' => array(
      'model' => 'Grade',
      'foreign_key' => 'grade_id'
    )
  );

  public function &__get($key)
  {
    $property =& parent::__get($key);

    if (!$property) {
      switch ($key) {
        case 'score':
          $property = new Score($this->score_whole, $this->score_partial);
          break;

        case 'number_of_ties':
          $property = $this->getTieQuery()
            ->where('place', '=', $this->place)
            ->count_all(true);
          break;

        case 'has_same_shots':
          if (is_null($this->shots))
            $property = 0;
          else {
            $escapedShots =
              $this->getInstance()->real_escape_string($this->shots);
            $property = $this->getTieQuery()
              ->where('shots', "= '$escapedShots'", "")
              ->where('place', '=', $this->place)
              ->count_all(true);
          }
          break;

        case 'has_equal_score_with_lower_ranked_ppl':
          $property = $this->getTieQuery()
            ->where('place', '>', $this->place)
            ->count_all(true);
          break;

        case 'has_equal_score_with_higher_ranked_ppl':
          $property = $this->getTieQuery()
            ->where('place', '<', $this->place)
            ->count_all(true);
          break;

        case 'is_tied':
          $property = $this->number_of_ties > 0 || $this->has_equal_score_with_higher_ranked_ppl > 0 || $this->has_equal_score_with_lower_ranked_ppl > 0;
          break;

        case 'shooterInfo':
          if (!$this->shooter->id) return '';
          $property = $this->shooter->details->flags;

          $entryForm = Database::factory('EventEntryForm')
            ->where('event_id', '=', $this->match->event_id)
            ->where('shooter_id', '=', $this->shooter->id)
            ->find();

          if ($entryForm->is_loaded()) {
            $flags = array();

            if ($entryForm->notes_object->is_veteran)
              $flags[] = 'V';
            if ($entryForm->notes_object->is_super_veteran)
              $flags[] = 'SV';
            if ($entryForm->notes_object->is_lady)
              $flags[] = 'L';
            if ($entryForm->notes_object->is_u25)
              $flags[] = 'U';
            if ($entryForm->notes_object->is_junior)
              $flags[] = 'J';
            if ($entryForm->notes_object->is_tyro)
              $flags[] = 'T';
            if ($entryForm->notes_object->is_service_person)
              $flags[] = 'SP';
            if ($entryForm->notes_object->is_police_person)
              $flags[] = 'PP';

            $property = array_unique(array_merge($property, $flags));
          }

          $property = join(' ', $property);
          break;

        case 'shooterEntryForm':
          $property = Database::factory('EventEntryForm')
            ->where('event_entry_forms.event_id', '=', $this->match->event_id)
            ->where('event_entry_forms.shooter_id', '=', $this->shooter_id)
            ->find();
          break;

        case 'team':
          $property = Database::factory('EventTeam');
          if ($this->is_loaded()) {
            $property = $property->join('EventTeam_Shooter')
              ->on('EventTeam_Shooter', 'event_teams_shooters.team_id', '=', 'event_teams.id')
              ->where('event_id', '=', $this->match->event_id)
              ->where('shooter_id', '=', $this->shooter_id)
              ->find();
          }
          break;

        case 'division':
          $property = Database::factory("Division")
            ->join("EventShooterDivision", null, true)
            ->on("EventShooterDivision", "event_shooter_divisions.division_id", "=", "divisions.id")
            ->where("shooter_id", "=", $this->shooter_id);
          if ($this->match->event->has_subevents) {
            $subeventId = $this->match->getSubEvent()->id;
          }
          if ($subeventId) {
            $property->where("subevent_id", "=", $subeventId);
          } else {
            $property->where("event_id", "=", $this->match->event_id);
          }

          $property = $property->find_all();
          if (count($property) == 1) {
            $property = array_pop($property);
          } else {
            $tmp = array_values(Database::factory("Division")
              ->order_by("order", "asc")
              ->limit(1)
              ->find_all()
            );
            $property = array_pop($tmp);
          }

          break;
      }
    }

    return $property;
  }

  private function getTieQuery() {
    $query = Database::factory('Result')
      ->where('results.id', '!=', $this->id)
      ->where('place', '!=', 0)
      ->where('score_whole', '=', $this->score_whole)
      ->where('score_partial', '=', $this->score_partial)
      ->where('match_id', '=', $this->match_id);
    if ($this->match->event->is_divisional) {
      $query
        ->join('Match', null, true)
        ->on('Match', 'results.match_id', '=', 'matches.id')
        ->join('EventShooterDivision')
        ->on('EventShooterDivision', 'event_shooter_divisions.shooter_id', '=', 'results.shooter_id')
        ->where('event_shooter_divisions.division_id', '=', $this->division->id);
      if ($this->match->event->has_subevents) {
        $subeventId = $this->match->getSubevent()->id;
      }
      if ($subeventId) {
        $query->where('event_shooter_divisions.subevent_id', '=', $subeventId);
      } else {
        $query->where('event_shooter_divisions.event_id', '=', $this->match->event_id);
      }
    } else {
      $query->where('grade_id', '=', $this->grade_id);
    }
    return $query;
  }

  public function find_all_divisional($ignorePlace = false) {
    $results = parent::find_all();

    usort($results, function ($a, $b) use ($ignorePlace) {
      if ($a->division->order == $b->division->order) {
        if ($ignorePlace || $a->place == 0 || $a->place == $b->place) {
          if ($a->score_whole == $b->score_whole) {
            return $a->score_partial < $b->score_partial ? 1 : -1;
          }
          return $a->score_whole < $b->score_whole ? 1 : -1;
        }
        return $a->place > $b->place ? 1 : -1;
      }
      return $a->division->order > $b->division->order ? 1 : -1;
    });

    return $results;
  }

  public function save()
  {
    $shooter_grade = Database::factory('Shooter_Grade')
      ->where('shooter_id', '=', $this->shooter_id)
      ->where('grade_id', '=', $this->grade_id)
      ->find();
    if (!$shooter_grade->is_loaded()) {
      $shooter_grade->shooter_id = $this->shooter_id;
      $shooter_grade->grade_id = $this->grade_id;
      $shooter_grade->save();
    }
    return parent::save();
  }

  public function __toString()
  {
    return $this->score->__toString();
  }

  // [master 3a155d0] For the old recalculate code check git history!
  public static function CalculatePlaces($results, $getId = null)
  {
    if (!is_callable($getId)) {
      $getId = function ($result) {
        return $result->id;
      };
    }

    $place              = 1;
    $id                 = -1;
    $tie_count          = 0;
    $last_score_whole   = -1;
    $last_score_partial = -1;
    foreach ($results as $result) {
      if ($getId($result) !== $id) {
        $place              = 1;
        $id                 = $getId($result);
        $tie_count          = 0;
        $last_score_whole   = -1;
        $last_score_partial = -1;
      }

      if ($result->score_whole === $last_score_whole
        && $result->score_partial === $last_score_partial
      ) {
        $result->place = $place - 1;
        $tie_count++;
      } else {
        $place += $tie_count;
        $result->place = $place++;
        $tie_count     = 0;
      }

      $last_score_whole   = $result->score_whole;
      $last_score_partial = $result->score_partial;
    }

    return $results;
  }

  public static function GroupResultsByIdFunc($results, $getId = null)
  {
    if (!is_callable($getId)) {
      $getId = function ($result) {
        return $result->grade_id;
      };
    }

    $tiedResults = array();
    foreach ($results as $result) {
      if (!isset($tiedResults[$getId($result)]))
        $tiedResults[$getId($result)] = array();
      if (!isset($tiedResults[$getId($result)][$result->place]))
        $tiedResults[$getId($result)][$result->place] = array();
      $tiedResults[$getId($result)][$result->place][] = $result;
    }

    return $tiedResults;
  }

  /**
   * Returns ungrouped results!
   *
   * TODO: This code needs refactoring badly.
   *
   * @param array $groupedResults
   * @param int $matchType
   * 0 - Unknown
   * 1 - Single Range
   * 2 - Daily Aggregate
   * 3 - Aggregate of daily aggregates (e.g. lead up or queens)
   * @return array
   */
  public static function UpdatePlacesWithCountBack($groupedResults, $matchType = 0)
  {
    $ungroupedResults = array();

    foreach ($groupedResults as $grade) {
      foreach ($grade as $place) {
        $tempCopy = array_values($place);
        usort($tempCopy, function ($a, $b) use ($matchType) {
          // For Daily aggregates and aggregate of daily aggregates don't count back first place
          if ($matchType > 1 && $a->place === 1 && $b->place === 1)
            return 0;

          // For Aggregate of aggregate ties that are not first place
          if ($matchType == 3 && $a->place > 1 && $a->place == $b->place) {
            $matches = $a->match->getSubAggregates();
            if (count($matches) == 0) $matches = $a->match->getMatchesOfAggregate();
            $matches = array_reverse($matches);
            foreach ($matches as $match) {
              $aResult = null;
              $bResult = null;
              foreach ($match->results as $result) {
                if ($result->shooter_id == $a->shooter_id)
                  $aResult = $result;
                if ($result->shooter_id == $b->shooter_id)
                  $bResult = $result;
                if ($aResult && $bResult)
                  break;
              }
              if ($aResult && $bResult) {
                $comparison = $aResult->score->compare($bResult->score);
                if ($comparison != 0)
                  return $comparison;
              }
            }
          }

          $shotValues = array('0' => 0.0, '1' => 1.0, '2' => 2.0, '3' => 3.0, '4' => 4.0, '5' => 5.0, 'V' => 5.1, '6' => 6.0, 'X' => 6.1);
          $aShots     = array_reverse(str_split($a->shots));
          $bShots     = array_reverse(str_split($b->shots));
          foreach ($aShots as $i => $v) {
            if ($bShots[$i] != $v)
              return $shotValues[$v] > $shotValues[$bShots[$i]] ? -1 : 1;
          }

          return 0;
        });

        $i          = 0;
        $tiedCount  = 0;
        $collection = new CachingIterator(new ArrayIterator($tempCopy), CachingIterator::TOSTRING_USE_CURRENT);
        foreach ($collection as $result) {
          $result->place = $result->place + $i;

          if ($collection->hasNext()) {
            $nextResult = $collection->getInnerIterator()->current();
            // Daily aggregate and Aggregate of daily aggregate don't count back first place
            if (($nextResult->shots === $result->shots)
              || ($matchType > 1 && $result->place == 1 && $nextResult->place == 1)) {
              $tiedCount++;
            } else {
              $i += $tiedCount + 1;
              $tiedCount = 0;
            }
          }

          $ungroupedResults[$result->id] = $result;
        }
      }
    }

    return $ungroupedResults;
  }
}
