<?php defined('ABSPATH') or die('No direct script access.');

/**
 * Module - Match Management
 */
class MatchManagementModule extends Module implements IModule
{
  protected $views_directory;
  protected $router;

  public function __construct($skip_roles = false)
  {
    parent::__construct($skip_roles);

    $this->views_directory = __DIR__ . '/views/';
    $this->router = new SimpleRouter(array(
      'base_url' => 'admin.php?page=' . basename(__FILE__)
    ));
    $this->router->setRoute('', array($this, 'index'))
      ->setRoute('list-matches', array($this, 'list_matches'))
      ->setRoute('edit-match', array($this, 'edit_match'))
      ->setRoute('delete-match', array($this, 'delete_match'))
      ->setRoute('cancel-match', array($this, 'cancel_match'));
  }

  public function index()
  {
    $this->handle_post($_POST);

    $pagination = new Pagination(Database::factory('Event'), $this->router->getUrl(''));
    $pagination->setSearchFields(array('id', 'name'));
    View::load($this->views_directory, 'select_event', array(
      'router' => $this->router,
      'pagination' => $pagination,
    ));
  }

  protected function handle_post($post)
  {
    if (!$post) return;

    if (isset($post['delete']) && $post['delete'] === 'match') {
      Database::factory('MatchModel', $post['id'])->delete();
      add_message('Match deleted.');
    } else if (isset($post['cancel']) && $post['cancel'] === 'match') {
      $match = Database::factory('MatchModel', $post['id'])->find();
      $match->is_cancelled = 1;
      $match->cancel_reason = $post['cancel_reason'];
      $match->save();
      add_message('Match cancelled!');
    } else if (isset($post['save']) && $post['save'] === 'match') {
      if (!Module::HasManageCapability("grades"))
        unset($post['is_graded']);

      $aggregates = $post['aggregates'];
      $badges = $post['badges'];
      unset($post['aggregates']);
      unset($post['badge']);
      unset($post['save']);

      $match = Database::factory('MatchModel', $post['id'])->find();
      if ($post['id'] && !$match->is_loaded()) {
        $this->do_404();
        exit;
      }
      if (!$post['is_cancelled']) {
        unset($post['cancel_reason']);
      }

      $match->values($post)
            ->save()
            ->removeAllBadges();

      foreach ($badges["grade_id"] as $gradeId => $numberOfBadges) {
        $badge = Database::factory("Match_Badge");
        $badge->match_id = $match->id;
        $badge->grade_id = $gradeId;
        $badge->number_of_badges = $numberOfBadges;
        $badge->save();
      }
      foreach ($badges["division_id"] as $divisionId => $numberOfBadges) {
        $badge = $match->getBadge($divisionId, false);
        if (!$badge) {
          $badge = Database::factory("Match_Badge");
          $badge->match_id = $match->id;
          $badge->division_id = $divisionId;
        }
        $badge->number_of_badges = $numberOfBadges;
        $badge->save();
      }

      // Easier to remove all the ranges and re-add them
      $match->removeAllRanges();
      foreach ($aggregates as $aggregate) {
        // data:
        // [0] => <subevent|range|match>-<id>
        // [1] => <subevent|range|match>
        // [2] => <id>
        preg_match('/^([\w]+)\-([\d]+)$/i', $aggregate, $data);
        $match->add($data[1], $data[2]);
      }
      add_message('Match saved.');

      return $match->id;
    } elseif (isset($post['matches'])) {
      // Reorder of matches
      $i = 1;
      foreach ($post['matches'] as $match_id) {
        $match = Database::factory('Match', $match_id)->find();
        $match->number = $i++;
        $match->save();
      }
    }

    if (!empty($_GET['returnTo'])) {
      $this->do_redirect(base64_decode($_GET['returnTo']));
      exit;
    }
  }

  public function list_matches()
  {
    $this->handle_post($_POST);
    $event_id = Arr::get('event_id');
    $event = Database::factory('Event', $event_id)->find();

    if (!$event->is_loaded()) {
      $this->do_404();
      return;
    }

    $matches = Database::factory('MatchModel')
      ->where('event_id', '=', $event_id)
      ->order_by('number', 'ASC')
      ->find_all();

    View::load($this->views_directory, 'list_matches', array(
      'router' => $this->router,
      'event' => $event,
      'matches' => $matches,
    ));
  }

  public function edit_match()
  {
    $this->handle_post($_POST);
    $matchId = Arr::get($_GET, 'match_id');

    $match = Database::factory('MatchModel', $matchId)->find();
    if (!$match->is_loaded()) {
      $match->event_id = Arr::get($_GET, 'event_id');
      $match->number = $match->getNextNumber();
    }

    $event = $match->event;
    $eligible_ranges = array();
    $eligible_subevents = array();
    $eligible_matches = Database::factory('MatchModel')
      ->where('event_id', '=', $event->id)
      ->where('number', '<', $match->number)
      ->find_all();

    if (count($event->ranges) > 0) {
      $eligible_ranges = $event->ranges;
    } else if (count($event->subevents) > 0) {
      $eligible_subevents = $event->subevents;
      foreach ($eligible_subevents as $subevent) {
        $eligible_ranges = array_merge($eligible_ranges, $subevent->ranges);
      }
    }

    $grades = Database::factory('Grade')->find_all();
    foreach ($grades as $grade) {
      if ($match->is_loaded()) {
        $badge = $match->getBadge($grade->id);
        $grade->badges = $badge ? $badge->number_of_badges : 0;
      } else {
        $grade->badges = 0;
      }
      $subevent = $match->getSubEvent();
      if ($subevent) $grade->badgeHeading = $subevent->name . ' - ';
      $grade->badgesHeading .= $grade->discipline->name . ' - ' . $grade->name;
    }
    $divisions = null;
    if ($event->is_divisional) {
      $divisions = Database::factory("Division")->find_all();
      foreach ($divisions as &$division) {
        if ($division->is_loaded()) {
          $badge = $match->getBadge($division->id, false);
          $division->badges = $badge ? $badge->number_of_badges : 0;
        } else {
          $division->badges = 0;
        }
      }
    }

    View::load($this->views_directory, 'edit_match', array(
      'router' => $this->router,
      'match' => $match,
      'categories' => $match->getCategories(),
      'eligible_subevents' => $eligible_subevents,
      'eligible_ranges' => $eligible_ranges,
      'eligible_matches' => $eligible_matches,
      'grades' => $grades,
      'divisions' => $divisions
    ));
  }

  public function delete_match()
  {
    $match = Database::factory('MatchModel', Arr::get('match_id'))->find();
    View::load($this->views_directory, 'delete_match', array(
      'router' => $this->router,
      'match' => $match,
      'hasResults' => $match->hasResults()
    ));
  }

  public function cancel_match()
  {
    View::load($this->views_directory, 'cancel_match', array(
      'router' => $this->router,
      'match' => Database::factory('MatchModel', Arr::get('match_id'))->find()
    ));
  }

  public function callback()
  {
    echo $this->router->execute();
  }

  public function init()
  {
    if (!current_user_can(Module::CAPABILITY_EVENT)) {
      return;
    }
    $this_obj =& $this;
    $page_slug = basename(__FILE__);

    add_action('admin_menu', function () use ($this_obj, $page_slug) {
      $page = add_submenu_page('EventManagementModule.php', __('Match Management'), __('Matches'), Module::CAPABILITY_EVENT, $page_slug, array($this_obj, 'callback'));
      add_action('admin_print_styles-' . $page, function () {
        wp_enqueue_script('jquery-cdn');
        wp_enqueue_script('jquery-migrate');
        wp_enqueue_script('jquery-ui-cdn');
        wp_enqueue_script('chosen');
        wp_enqueue_script('nraa-admin-scripts');
        wp_enqueue_script('nraa-admin-matches');
        wp_enqueue_style('chosen');
        wp_enqueue_style('jquery-ui-styles');
      });
    });
    add_action('wp_before_admin_bar_render', function () use ($page_slug) {
      NraaMenuBar::getInstance()->add_sub_menu('Matches', get_admin_url(null, 'admin.php?page=' . $page_slug), SHORTNAME . '_management');
    });
  }
}
