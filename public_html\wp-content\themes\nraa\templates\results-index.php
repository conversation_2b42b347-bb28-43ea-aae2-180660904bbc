<?php
/**
 * Template Name: Results Index
 */

get_header();

// Get all published results pages
$results_pages = get_posts(array(
  'post_type' => 'page',
  'post_status' => 'publish',
  'meta_query' => array(
    array(
      'key' => '_wp_page_template',
      'value' => 'templates/results.php'
    )
  ),
  'numberposts' => -1,
  'orderby' => 'title',
  'order' => 'ASC'
));

// Group results by year
$results_by_year = array();
foreach ($results_pages as $page) {
  // Extract year from title or use current year as fallback
  preg_match('/(\d{4})/', $page->post_title, $matches);
  $year = isset($matches[1]) ? $matches[1] : date('Y');
  
  if (!isset($results_by_year[$year])) {
    $results_by_year[$year] = array();
  }
  
  $results_by_year[$year][] = $page;
}

// Sort years in descending order
krsort($results_by_year);
?>

<div class="results-index-page">
  <header class="results-index-header">
    <h1><?php the_title(); ?></h1>
    
    <?php
    while (have_posts()) {
      the_post();
      $content = get_the_content();
      if ($content) {
        echo '<div class="results-index-description">' . $content . '</div>';
      }
    }
    ?>
    
    <div class="results-index-controls">
      <div class="search-container">
        <input type="text" id="results-index-search" placeholder="Search competitions by name, location, or type..." class="search-bar">
        <button type="button" id="clear-index-search" class="ui-button" style="display: none;">Clear</button>
      </div>
      
      <div class="filter-container">
        <label for="year-filter">Filter by Year:</label>
        <select id="year-filter">
          <option value="">All Years</option>
          <?php foreach (array_keys($results_by_year) as $year): ?>
            <option value="<?php echo $year; ?>"><?php echo $year; ?></option>
          <?php endforeach; ?>
        </select>
        
        <label for="type-filter">Filter by Type:</label>
        <select id="type-filter">
          <option value="">All Types</option>
          <option value="championship">Championships</option>
          <option value="prize">Prize Meetings</option>
          <option value="open">Open Competitions</option>
          <option value="queens">Queen's/King's Prize</option>
          <option value="national">National Events</option>
        </select>
      </div>
    </div>
  </header>

  <div class="results-index-content">
    <?php if (empty($results_pages)): ?>
      <div class="no-results-message">
        <h3>No Results Available</h3>
        <p>There are currently no published results available.</p>
      </div>
    <?php else: ?>
      
      <div class="results-summary">
        <div class="summary-stats">
          <div class="stat-item">
            <span class="stat-number"><?php echo count($results_pages); ?></span>
            <span class="stat-label">Total Competitions</span>
          </div>
          <div class="stat-item">
            <span class="stat-number"><?php echo count($results_by_year); ?></span>
            <span class="stat-label">Years Covered</span>
          </div>
          <div class="stat-item">
            <span class="stat-number" id="visible-count"><?php echo count($results_pages); ?></span>
            <span class="stat-label">Showing</span>
          </div>
        </div>
      </div>

      <?php foreach ($results_by_year as $year => $year_results): ?>
        <div class="year-section" data-year="<?php echo $year; ?>">
          <h2 class="year-heading">
            <span class="year-title"><?php echo $year; ?> Results</span>
            <span class="year-count">(<?php echo count($year_results); ?> competitions)</span>
          </h2>
          
          <div class="results-grid">
            <?php foreach ($year_results as $result_page): ?>
              <?php
              // Determine competition type based on title
              $title = $result_page->post_title;
              $type = '';
              if (stripos($title, 'championship') !== false) $type = 'championship';
              elseif (stripos($title, 'prize') !== false) $type = 'prize';
              elseif (stripos($title, 'open') !== false) $type = 'open';
              elseif (stripos($title, 'queen') !== false || stripos($title, 'king') !== false) $type = 'queens';
              elseif (stripos($title, 'national') !== false) $type = 'national';
              
              // Extract location/state
              $location = '';
              if (preg_match('/\(([A-Z]{2,3})\)/', $title, $matches)) {
                $location = $matches[1];
              }
              ?>
              
              <div class="result-card" 
                   data-title="<?php echo esc_attr(strtolower($title)); ?>"
                   data-type="<?php echo esc_attr($type); ?>"
                   data-location="<?php echo esc_attr(strtolower($location)); ?>"
                   data-year="<?php echo $year; ?>">
                
                <div class="card-header">
                  <h3 class="card-title">
                    <a href="<?php echo get_permalink($result_page->ID); ?>">
                      <?php echo $result_page->post_title; ?>
                    </a>
                  </h3>
                  
                  <div class="card-meta">
                    <?php if ($type): ?>
                      <span class="type-badge type-<?php echo $type; ?>"><?php echo ucfirst($type); ?></span>
                    <?php endif; ?>
                    
                    <?php if ($location): ?>
                      <span class="location-badge"><?php echo $location; ?></span>
                    <?php endif; ?>
                    
                    <span class="year-badge"><?php echo $year; ?></span>
                  </div>
                </div>
                
                <div class="card-content">
                  <?php if ($result_page->post_excerpt): ?>
                    <p class="card-excerpt"><?php echo $result_page->post_excerpt; ?></p>
                  <?php endif; ?>
                  
                  <div class="card-actions">
                    <a href="<?php echo get_permalink($result_page->ID); ?>" class="view-results-btn ui-button">
                      View Results
                    </a>
                  </div>
                </div>
              </div>
            <?php endforeach; ?>
          </div>
        </div>
      <?php endforeach; ?>
      
      <div class="no-results-found" style="display: none;">
        <h3>No competitions found</h3>
        <p>Try adjusting your search terms or filters.</p>
      </div>
      
    <?php endif; ?>
  </div>
</div>

<?php get_footer(); ?>

<style>
/* Results Index Page Styling */
.results-index-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.results-index-header {
  margin-bottom: 30px;
}

.results-index-description {
  margin: 15px 0;
  padding: 15px;
  background: #f8f9fa;
  border-left: 4px solid #23408E;
  border-radius: 4px;
}

.results-index-controls {
  background: #fff;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-container {
  margin-bottom: 15px;
}

.search-container input[type="text"] {
  width: 400px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-container label {
  font-weight: bold;
  color: #23408E;
}

.filter-container select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}

.results-summary {
  background: linear-gradient(135deg, #23408E 0%, #475876 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 2.5em;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 0.9em;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.year-section {
  margin: 40px 0;
}

.year-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 3px solid #23408E;
  margin-bottom: 20px;
}

.year-title {
  font-size: 1.8em;
  color: #23408E;
}

.year-count {
  font-size: 0.9em;
  color: #666;
  font-weight: normal;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.result-card {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.card-header {
  padding: 20px 20px 10px;
}

.card-title {
  margin: 0 0 10px;
  font-size: 1.1em;
  line-height: 1.3;
}

.card-title a {
  color: #23408E;
  text-decoration: none;
  font-weight: bold;
}

.card-title a:hover {
  color: #1a2f6b;
  text-decoration: underline;
}

.card-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.type-badge, .location-badge, .year-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.type-badge.type-championship {
  background: #4CAF50;
  color: white;
}

.type-badge.type-prize {
  background: #2196F3;
  color: white;
}

.type-badge.type-open {
  background: #FF9800;
  color: white;
}

.type-badge.type-queens {
  background: #9C27B0;
  color: white;
}

.type-badge.type-national {
  background: #f44336;
  color: white;
}

.location-badge {
  background: #607D8B;
  color: white;
}

.year-badge {
  background: #795548;
  color: white;
}

.card-content {
  padding: 0 20px 20px;
}

.card-excerpt {
  color: #666;
  font-size: 0.9em;
  line-height: 1.4;
  margin: 10px 0;
}

.card-actions {
  margin-top: 15px;
}

.view-results-btn {
  display: inline-block;
  padding: 8px 16px;
  font-size: 0.9em;
  text-decoration: none;
}

.no-results-message, .no-results-found {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-results-message h3, .no-results-found h3 {
  color: #23408E;
  margin-bottom: 10px;
}

.quick-nav {
  background: #f8f9fa;
  padding: 15px 20px;
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid #ddd;
}

.quick-nav h4 {
  margin: 0 0 10px;
  color: #23408E;
  font-size: 1em;
}

.nav-years {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.nav-year-link {
  padding: 6px 12px;
  background: #23408E;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-size: 0.9em;
  transition: background 0.2s ease;
}

.nav-year-link:hover {
  background: #1a2f6b;
  color: white;
  text-decoration: none;
}

@media (max-width: 768px) {
  .results-index-controls {
    padding: 15px;
  }

  .search-container input[type="text"] {
    width: 100%;
  }

  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-container select {
    min-width: auto;
    width: 100%;
    margin-bottom: 10px;
  }

  .summary-stats {
    flex-direction: column;
    gap: 20px;
  }

  .year-heading {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .card-meta {
    justify-content: flex-start;
  }
}
</style>

<script>
$(document).ready(function() {
  // Initialize search and filter functionality
  function initializeIndexSearch() {
    var $searchInput = $('#results-index-search');
    var $clearButton = $('#clear-index-search');
    var $yearFilter = $('#year-filter');
    var $typeFilter = $('#type-filter');
    var $visibleCount = $('#visible-count');
    var searchTimeout;

    function performFilter() {
      var searchTerm = $searchInput.val().toLowerCase().trim();
      var selectedYear = $yearFilter.val();
      var selectedType = $typeFilter.val();

      var $cards = $('.result-card');
      var $yearSections = $('.year-section');
      var visibleCount = 0;

      // Show/hide clear button
      if (searchTerm === '') {
        $clearButton.hide();
      } else {
        $clearButton.show();
      }

      // Filter cards
      $cards.each(function() {
        var $card = $(this);
        var title = $card.data('title') || '';
        var type = $card.data('type') || '';
        var location = $card.data('location') || '';
        var year = $card.data('year') || '';

        var matchesSearch = searchTerm === '' ||
          title.indexOf(searchTerm) !== -1 ||
          location.indexOf(searchTerm) !== -1 ||
          type.indexOf(searchTerm) !== -1;

        var matchesYear = selectedYear === '' || year == selectedYear;
        var matchesType = selectedType === '' || type === selectedType;

        if (matchesSearch && matchesYear && matchesType) {
          $card.show();
          visibleCount++;
        } else {
          $card.hide();
        }
      });

      // Show/hide year sections based on visible cards
      $yearSections.each(function() {
        var $section = $(this);
        var $visibleCards = $section.find('.result-card:visible');

        if ($visibleCards.length > 0) {
          $section.show();
          // Update year count
          $section.find('.year-count').text('(' + $visibleCards.length + ' competitions)');
        } else {
          $section.hide();
        }
      });

      // Update visible count
      $visibleCount.text(visibleCount);

      // Show/hide no results message
      if (visibleCount === 0) {
        $('.no-results-found').show();
      } else {
        $('.no-results-found').hide();
      }
    }

    // Event handlers
    $searchInput.on('input', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(performFilter, 300);
    });

    $yearFilter.on('change', performFilter);
    $typeFilter.on('change', performFilter);

    $clearButton.click(function() {
      $searchInput.val('');
      $yearFilter.val('');
      $typeFilter.val('');
      performFilter();
    });
  }

  // Initialize card hover effects
  function initializeCardEffects() {
    $('.result-card').hover(
      function() {
        $(this).find('.card-title a').css('color', '#1a2f6b');
      },
      function() {
        $(this).find('.card-title a').css('color', '#23408E');
      }
    );
  }

  // Initialize quick navigation
  function initializeQuickNav() {
    // Add quick year navigation
    var $yearHeadings = $('.year-heading');
    if ($yearHeadings.length > 1) {
      var $quickNav = $('<div class="quick-nav"><h4>Quick Navigation:</h4></div>');
      var $navList = $('<div class="nav-years"></div>');

      $yearHeadings.each(function() {
        var $heading = $(this);
        var year = $heading.closest('.year-section').data('year');
        var $navItem = $('<a href="#year-' + year + '" class="nav-year-link">' + year + '</a>');

        // Add ID to year section for smooth scrolling
        $heading.closest('.year-section').attr('id', 'year-' + year);

        $navList.append($navItem);
      });

      $quickNav.append($navList);
      $('.results-index-controls').after($quickNav);

      // Smooth scrolling for navigation links
      $('.nav-year-link').click(function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        $('html, body').animate({
          scrollTop: $(target).offset().top - 20
        }, 500);
      });
    }
  }

  // Initialize all functionality
  initializeIndexSearch();
  initializeCardEffects();
  initializeQuickNav();

  // Add loading states for better UX
  $(document).ajaxStart(function() {
    $('body').addClass('loading');
  }).ajaxStop(function() {
    $('body').removeClass('loading');
  });
});
</script>
