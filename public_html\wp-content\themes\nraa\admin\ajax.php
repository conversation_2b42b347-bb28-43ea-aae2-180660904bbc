<?php

/**
 * AJAX Methods
 */
function echoJSON($mixed)
{
  header('Content-Type: application/json');
  echo json_encode($mixed);
  exit();
}

/** TODO: Need to check security **/
function nraa_ajax_get()
{
  $model = Arr::get($_GET, 'model');
  if (!$model)
    echoJSON(false);

  $id = Arr::get($_GET, 'id', null);
  $limit = intval(Arr::get($_GET, 'limit'));
  $where = Arr::get($_GET, 'where');
  if ($id)
    $response = Database::factory($model, $id)->find_as_array();
  else {
    $data = Database::factory($model);

    if ($where)
      $data->where($where['key'], $where['op'], $where['value']);

    if ($limit)
      $data->limit($limit);

    $response = array_values($data->find_all_as_array());
  }
  echoJSON($response);
}

function nraa_ajax_entry_form()
{
  unset($_POST['action'], $_POST['confirm']);
  $id = Arr::get($_POST, 'entry_form_id');
  try {
    $result = Database::factory('EventEntryForm', $id)
      ->handleFormPost($_POST);
  } catch (Exception $e) {
    $result = array(
      'success' => false,
      'errorMessage' => 'Unexpected error occurred',
      '_debug' => $e);
  }

  if ($result['success']) {
    $entryType = $id ? 'Updated' : 'New';
    $message = <<< EOT
<table>
    <tr> <th colspan="2" align="center" style="background:#f9f9f9;">%s Entry Form</th> </tr>
    <tr> <th align="left">Date:</th><td>%s</td> </tr>
    <tr> <th align="left">Event:</th><td>%s</td> </tr>
    <tr> <th align="left">Entry #:</th><td>%s</td> </tr>
    <tr> <th align="left">Shooter:</th><td>%s</td> </tr>
    <tr> <td colspan="2" align="center"><a href="%s">Process Entry Form</a></td> </tr>
</table>
EOT;
    add_filter('wp_mail_content_type', function () {
      return 'text/html';
    });
    wp_mail(
      get_option('nraa_notification_email', '<EMAIL>'),
      "{$entryType} Competition Entry",
      sprintf($message,
        $entryType,
        date('d-m-Y'),
        $result['event'],
        $result['entry_number'],
        $result['shooter'],
        get_admin_url(null, 'admin.php?page=EventManagementModule.php&action=entry-form-edit&entry_form_id=' . $result['id'])
      )
    );
  }
  echoJSON($result);
}

function nraa_ajax_get_shooter_details() {
  $data = false;

  $shooterId = Arr::get('shooter_id');
  $shooter = Database::factory('Shooter', $shooterId)->find();
  if ($shooter->is_loaded()) {
    $data = array();
    $data['id'] = $shooterId;
    $data['sid'] = $shooter->sid;
    $data['club'] = $shooter->club;
    $data['results'] = Database::factory('Result')
      ->where('shooter_id', '=', $shooterId)
      ->count_all();
    $details = Database::factory('ShooterDetails')
      ->where('shooter_id', '=', $shooterId)
      ->find();
    $data['details'] = array(
      'first_name' => $details->first_name,
      'last_name' => $details->last_name,
      'preferred_name' => $details->preferred_name
    );
  }

  echoJSON($data);
}

function nraa_ajax_is_already_entered()
{
  $shooter_id = Arr::get($_GET, 'shooter_id');
  $event_id = Arr::get($_GET, 'event_id');
  $count = Database::factory('EventEntryForm')
    ->where('event_id', '=', $event_id)
    ->where('shooter_id', '=', $shooter_id)
    ->count_all();
  echoJSON($count > 0);
}

function nraa_ajax_get_entries_for_match()
{
  $match_id = Arr::get($_GET, 'match_id');
  $event_id = Arr::get($_GET, 'event_id');
  $start = intval(Arr::get($_GET, 'start', 0));
  $count = intval(Arr::get($_GET, 'count', 0));
  $team_id = Arr::get($_GET, 'team_id');
  $entries = Database::factory('EventEntryForm')
    ->join('MatchModel', null, true)
    ->on('MatchModel', 'matches.event_id', '=', 'event_entry_forms.event_id')
    ->join('Result', 'LEFT OUTER', true)
    ->on('Result', 'results.shooter_id', '=', 'event_entry_forms.shooter_id')
    ->on('Result', 'results.match_id', '=', 'matches.id')
    ->where('event_entry_forms.event_id', '=', $event_id)
    ->where('event_entry_forms.status', '=', 'paid')
    ->where('matches.id', '=', $match_id)
    ->order_by('entry_number');
  if ($start && $count) {
    $entries = $entries->where('entry_number', '>=', $start)
      ->limit($count);
  }

  if ($team_id) {
    $entries = $entries
      ->join("EventTeam", null, true)
      ->on("EventTeam", 'event_teams.event_id', '=', 'event_entry_forms.event_id')
      ->join("EventTeam_Shooter", null, true)
      ->on("EventTeam_Shooter", 'event_teams_shooters.team_id', '=', "event_teams.id")
      ->on("EventTeam_Shooter", 'event_teams_shooters.shooter_id', '=', 'event_entry_forms.shooter_id')
      ->where('event_teams_shooters.team_id', '=', $team_id);
    // ->where('event_teams_shooters.shooter_id', '=', 'event_entry_forms.shooter_id');
    // ->on("EventTeam_Shooter", 'event_teams_shooters.team_id', '=', $team_id)
    // ->where('event_entry_forms.shooter_id', '=', 'event_teams_shooters.shooter_id');
  }

  $entries = $entries->find_all_as_array();
  $entries = array_map(function ($entry) {
    $entry['notes'] = json_decode($entry['notes']);

    $subEntries = Database::factory('EventEntryFormSubEvent')
      ->where('event_entry_form_id', '=', $entry['id'])
      ->find_all();

    $subEntryArray = array();
    foreach ($subEntries as $se)
      $subEntryArray[$se->id] = array('grade_id' => $se->grade_id, 'subevent_id' => $se->subevent_id);

    $entry['subevents'] = array_values($subEntryArray);

    return $entry;
  }, $entries);
  echoJSON(array_values($entries));
}

function nraa_ajax_get_entered_shooters()
{
  $event_id = Arr::get($_GET, 'event_id');
  $shooters = Database::factory('Shooter')
    ->join('EventEntryForm', null, true)
    ->on('EventEntryForm', 'event_entry_forms.shooter_id', '=', 'shooters.id')
    ->join('ShooterDetails')
    ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'shooters.id')
    ->where('event_entry_forms.event_id', '=', $event_id)
    ->find_all_as_array();
  echoJSON(array_values($shooters));
}

function nraa_ajax_guess_shooter_grade()
{
  $shooter_id = intval(Arr::get($_GET, 'shooter_id'));
  $discipline_id = intval(Arr::get($_GET, 'discipline_id'));
  if (!$shooter_id || !$discipline_id)
    echoJSON(false);


  $shooter_calculated_grade = Database::factory('ShooterCalculatedGrade')
    ->join('Grade')
    ->on('Grade', 'grades.discipline_id', '=', $discipline_id)
    ->on('Grade', 'grades.id', '=', 'shooter_calculated_grades.grade_id')
    ->where('shooter_id', '=', $shooter_id)
    ->order_by('grades.threshold', 'DESC')
    ->find();
  if ($shooter_calculated_grade->is_loaded()) {
    $shooterGrade = $shooter_calculated_grade->grade;
  } else {
    $shooterGrade = Database::factory('Grade')
      ->join('Shooter_Grade')
      ->on('Shooter_Grade', 'shooters_grades.grade_id', '=', 'grades.id')
      ->where('shooters_grades.shooter_id', '=', $shooter_id)
      ->where('grades.discipline_id', '=', $discipline_id)
      ->find();
    if (!$shooterGrade->is_loaded())
      $shooterGrade = Database::factory('Grade')
        ->where('discipline_id', '=', $discipline_id)
        ->order_by('threshold', 'ASC')
        ->find();
  }
  $shooterGrade->otherGrades = array();
  foreach ($shooterGrade->discipline->grades as $grade)
    $shooterGrade->otherGrades[] = array('id' => $grade->id, 'name' => $grade->name);

  $shooterGrade->set_is_loaded(true); // make sure it doesn't find again
  // FIXME: don't need to wrap this in array just have to fix coffee script code
  echoJSON(array($shooterGrade->find_as_array()));
}

function nraa_ajax_get_shooter_grade()
{
  $shooter_id = Arr::get($_GET, 'shooter_id');
  $match_id = Arr::get($_GET, 'match_id');
  if (!$shooter_id || !$match_id)
    echoJSON(false);

  $match = Database::factory('Match', $match_id)->find();
  // Check if this event is using entry form
  if ($match->event->is_using_entry_form) {
    // Get this shooters entry form
    $entryForm = array_shift(array_filter($match->event->entry_forms, function ($entryForm) use ($shooter_id) {
      return $entryForm->shooter_id === $shooter_id;
    }));
    // Get the shooters grade from the entry form
    // but first check if the event has subevents
    if ($match->event->has_subevents) { // Now we need to find the entry form for the subevent
      // but first we need the subevents id
      $range = current($match->ranges);
      $subevent_id = $range->subevent->id;
      $entryForm = array_shift(array_filter($entryForm->subeventEntries, function ($entryForm) use ($subevent_id) {
        return $entryForm->subevent_id === $subevent_id;
      }));
      // Okay! finally we can get the grade_id
    }
    if ($entryForm !== null)
      echoJSON($entryForm->grade->find_as_array());
  }

  // Otherwise find if the shooter has shot in this event/cup already and grab the grade he shot in
  /*
  Example Query:
  SELECT * FROM grades
  JOIN results ON results.grade_id = grades.id
  JOIN shooters ON shooters.id = results.id
  LEFT OUTER JOIN events_ranges ON events_ranges.range_id = 2
  LEFT OUTER JOIN subevents_ranges ON subevents_ranges.range_id = 2
  WHERE shooters.id = 3
  GROUP BY grades.id;
  */
  $grades = Database::factory('Grade')
    ->join('Result')
    ->on('Result', 'results.grade_id', '=', 'grades.id')
    ->join('Shooter')
    ->on('Shooter', 'shooters.id', '=', 'results.id')
    ->join('Event_Range', 'LEFT OUTER')
    ->on('Event_Range', 'events_ranges.range_id', '=', $match->range->id)
    ->join('SubEvent_Range', 'LEFT OUTER')
    ->on('SubEvent_Range', 'subevents_ranges.range_id', '=', $match->range->id)
    ->where('shooters.id', '=', $shooter_id)
    ->group_by('grades.id')
    ->find_all();

  if (count($grades) > 0) {
    $grade = array_shift($grades);
    echoJSON($grade->find_as_array());
  }
  echoJSON(false);
}

function nraa_ajax_get_calculated_grades()
{
  $shooter = Database::factory('Shooter', Arr::get($_GET, 'shooter_id'))->find();
  if (!$shooter->is_loaded())
    echoJSON(false);

  $calculatedGrades = Database::factory('ShooterCalculatedGrade')
    ->where('shooter_calculated_grades.shooter_id', '=', $shooter->id)
    ->join('Grade', null, true)
    ->on('Grade', 'grades.id', '=', 'shooter_calculated_grades.grade_id')
    ->join('Discipline', null, true)
    ->on('Discipline', 'disciplines.id', '=', 'grades.discipline_id')
    ->join('Shooter', null, true)
    ->on('Shooter', 'shooters.id', '=', 'shooter_calculated_grades.shooter_id')
    ->join('ShooterDetails', null, true)
    ->on('ShooterDetails', 'shooter_details.shooter_id', '=', 'shooters.id')
                    //    ->order_by('')
    ->find_all_as_array_cb(function ($row) {
      return array(
        'Discipline' => array(
          'id' => $row['Discipline.id'],
          'name' => $row['Discipline.name'],
        ),
        'Grade' => array(
          'id' => $row['Grade.id'],
          'name' => $row['Grade.name'],
          'threshold' => $row['Grade.threshold'],
        ),
        'Shooter' => array(
          'id' => $row['Shooter.id'],
          'name' => sprintf('%s %s',
            (!empty($row['ShooterDetails.preferred_name'])
              ? $row['ShooterDetails.preferred_name']
              : $row['ShooterDetails.first_name']),
            $row['ShooterDetails.last_name']),
        ),
        'avg_score' => $row['avg_score'],
        'number_of_shoots' => $row['number_of_shoots'],
      );
    }, false);

  echoJSON($calculatedGrades);
}

// FIXME: THIS IS DUPLICATED CODE FROM SHOOTER MANAGEMENT MODULE
function nraa_ajax_grading_details()
{
  $shooter = Database::factory('Shooter', Arr::get('shooter_id'))->find();
  $discipline = Database::factory('Discipline', Arr::get('discipline_id'))->find();

  if (!$discipline->is_loaded() || !$shooter->is_loaded())
    echoJSON(false);

  $results = Database::factory('Result')
    ->join('MatchModel', null, true)
    ->on('MatchModel', 'results.match_id', '=', 'matches.id')
    ->join('Grade', null, true)
    ->on('Grade', 'results.grade_id', '=', 'grades.id')
    ->join('Event', null, true)
    ->on('Event', 'matches.event_id', '=', 'events.id')
    ->where('results.shooter_id', '=', $shooter->id)
    ->where('grades.discipline_id', '=', $discipline->id)
    ->where('matches.is_graded', '=', 1)
    ->where('matches.is_cancelled', '=', 0)
// By Catherines request, now showing all results
//    ->where('events.end_date', '>=', strtotime(GRADING_START_DATE))// Default: (2 years ago)
    ->where('events.is_team_event', '=', 0)
    ->order_by('events.end_date', 'DESC')
    ->order_by('matches.number', 'DESC')
//    ->limit(MAX_SHOOTS)
    ->find_all();

  $response = array();
  foreach (array_values($results) as $i => $result) {
    $highScore = $result->match->getHighScoreResult($result->grade->discipline_id);
    $result->match->highScore = $highScore;
    $result->scorePercentage = $result->score->percentageOf($highScore->score);
    $isUsed = $i < MAX_SHOOTS;
    array_push($response, array(
      'isUsed' => $isUsed,
      'eventName' => $result->match->event->name,
      'matchNumber' => $result->match->number,
      'highScore' => $highScore.'',
      'score' => $result.'',
      'scorePercentage' => substr(number_format($result->scorePercentage, 10), 0, -8)
    ));
  }
  echoJSON($response);
}

function calculate_score(string $shots): Score {
  $scoreWhole = 0;
  $scorePartial = 0;
  $shots = strtoupper($shots);
  foreach (str_split($shots) as $shot) {
    if ($shot == 'V' || $shot == 'X') {
      $scorePartial += 1;
      $scoreWhole += $shot == 'V' ? 5 : 6;
    } else {
      $scoreWhole += intval($shot);
    }
  }
  return new Score($scoreWhole, $scorePartial);
}

function error_exit_hexta(string $message): void {
  Database::transactionRollback();
  Queue::enqueue($topic, $payload);
  exit;
}

function nraa_ajax_perform_calcs() {
  $event_id = Arr::get($_REQUEST, 'event_id');
  $matches = Arr::get($_REQUEST, 'matches');
  $topic = Arr::get($_REQUEST, 'topic');
  $rmm = new ResultManagementModule(false, $topic);
  $rmm->performAutomatedCalculations(
    true,
    true,
    $event_id,
    $matches
  );
}

function nraa_ajax_read_queue() {
  echoJSON(Queue::dequeue($_REQUEST['topic']));
}

function nraa_ajax_upload_hexta()
{
  /* Validate the file uploaded was correct */
  if (!isset($_FILES['results']) || !$_FILES['results']['size']) {
    echoJSON(["error" => "You must upload the results file"]);
  }

  if ($_FILES['results']['type'] !== 'application/json') {
    echoJSON(["error" => "Uploaded file must be JSON but found {$_FILES['results']['type']}"]);
  }

  $contents = file_get_contents($_FILES['results']['tmp_name']);
  $upload_dir = wp_upload_dir('2021/06', true, true);
  $upload_file = $upload_dir['path'] . '/' . date('YmdHi') . '_' . basename($_FILES['results']['name']);
  move_uploaded_file($_FILES['results']['tmp_name'], $upload_file);

  $event_id = Arr::get($_REQUEST, 'event_id');
  $event = Database::factory('Event', $event_id)->find();
  if (!$event->is_loaded()) {
    echoJSON(["error" => "Could not load event"]);
  }

  $results_count = 0;
  $matches = [];

  try {
    Database::transactionStart();
    $uploaded_results = json_decode($contents);
    $hexta = new Hexta([
      "json" => $uploaded_results,
      "event_id" => $event->id,
      "match_ids" => array_map(function ($m) { return $m->id; }, array_values($event->matches)),
      "hexta_competition_id" => Arr::get($_REQUEST, 'hexta_competition_id')
    ]);
    $results = $hexta->convert();

    foreach ($results as $day) {
      // Don't know if I need this for anything... yet
      // $date = $day->date;
      foreach ($day->results as $result) {
        $score = calculate_score($result->shots);
        $shooter = Database::factory('Shooter')
          ->where('sid', '=', $result->SID)
          ->find();

        if (!$shooter->is_loaded()) {
          error_exit_hexta("Could not find shooter with SID: '{$result->SID}'");
        }

        $grade = Database::factory('Grade', $result->grade_id)->find();
        if (!$grade->is_loaded()) {
          error_exit_hexta("Could not find grade: '{$result->grade_id}'");
        }

        $match = Database::factory('Match', $result->match_id)->find();
        if (!$match->is_loaded()) {
          error_exit_hexta("Could not find match: '{$result->match_id}'");
        }

        Database::factory('Result')
          ->where('match_id', '=', $match->id)
          ->where('grade_id', '=', $grade->id)
          ->where('shooter_id', '=', $shooter->id)
          ->find()
          ->values(array(
            'match_id'      => $match->id,
            'shooter_id'    => $shooter->id,
            'grade_id'      => $grade->id,
            'place'         => 0,
            'shots'         => $result->shots,
            'score_whole'   => $score->whole,
            'score_partial' => $score->partial
          ))
          ->save();
        $matches[] = $match->id;
        $results_count += 1;
      }
      if ($has_error) break;
    }
    Database::transactionCommit();
  } catch (Exception $ex) {
    error_exit_hexta("An error occurred while processing upload: $ex");
    $has_error = true;
    Database::transactionRollback();
  }

  if (!$has_error) {
    echoJSON([
      "done" => "Successfully added $results_count results",
      "event_id" => $event_id,
      "matches" => array_unique($matches)
    ]);
  }
}
