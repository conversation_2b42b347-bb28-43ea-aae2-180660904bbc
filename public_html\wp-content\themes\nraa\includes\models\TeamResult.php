<?php defined('ABSPATH') or die('No direct script access.');

/**
 * Model for NRAA Event Results
 *
 * Ali Almaktoum Zenkey Group Pty Ltd
 */
class TeamResult extends Database
{
    protected $_tablename = "team_results";
    protected $_belongs_to = array(
        'match' => array(
            'model' => 'MatchModel',
            'foreign_key' => 'match_id',
        ),
        'team' => array(
            'model' => 'EventTeam',
            'foreign_key' => 'team_id',
        ),
        'grade' => array(
            'model' => 'Grade',
            'foreign_key' => 'grade_id'
        )
    );

    public function &__get($key) {
        $property =& parent::__get($key);

        if (!$property) {
            switch ($key) {
            case 'score':
				$property = new Score($this->score_whole, $this->score_partial);
				break;

            case 'number_of_ties':
                $property = Database::factory('TeamResult')
                    ->where('id', '!=', $this->id)
                    ->where('place', '!=', 0)
                    ->where('place', '=', $this->place)
                    ->where('match_id', '=', $this->match_id)
                    ->where('grade_id', '=', $this->grade_id)
                    ->count_all();
                break;
            case 'has_same_shots':
                
                if (is_null($this->shots)) :
                    $property = 0;
                else:
                   $property = Database::factory('TeamResult')
                    ->where('id', '!=', $this->id)
                    ->where('shots', '=', $this->shots)
                    ->where('place', '!=', 0)
                    ->where('place', '=', $this->place)
                    ->where('match_id', '=', $this->match_id)
                    ->where('grade_id', '=', $this->grade_id)                    
                    ->count_all();         
                endif;
               
                break;           
            case 'has_equal_score_with_lower_ranked_ppl':
                $property = Database::factory('TeamResult')
                    ->where('id', '!=', $this->id)
                    ->where('place', '!=', 0)
                    ->where('place', '>', $this->place)
                    ->where('score_whole', '=', $this->score_whole)
                    ->where('score_partial', '=', $this->score_partial)
                    ->where('match_id', '=', $this->match_id)
                    ->where('grade_id', '=', $this->grade_id)
                    ->count_all();
                break;
              case 'has_equal_score_with_higher_ranked_ppl':
                $property = Database::factory('TeamResult')
                    ->where('id', '!=', $this->id)
                    ->where('place', '!=', 0)
                    ->where('place', '<', $this->place)
                    ->where('score_whole', '=', $this->score_whole)
                    ->where('score_partial', '=', $this->score_partial)
                    ->where('match_id', '=', $this->match_id)
                    ->where('grade_id', '=', $this->grade_id)
                    ->count_all();
                break;
            case 'is_tied':
                $property = $this->number_of_ties > 0 || $this->has_equal_score_with_higher_ranked_ppl > 0 || $this->has_equal_score_with_lower_ranked_ppl > 0;
                break;
/*
            case 'shooterInfo':
                $property = '';
                if ($this->shooter->is_lady)
                    $property .= 'L';
                if ($this->shooter->is_veteran)
                    $property .= 'V';
                if ($this->shooter->is_u25)
                    $property .= 'U';
                break;

            case 'shooterEntryForm':
                $property = Database::factory('EventEntryForm')
                    ->where('event_entry_forms.event_id', '=', $this->match->event_id)
                    ->where('event_entry_forms.shooter_id', '=', $this->shooter_id)
                    ->find();
                break;

            case 'team':
                $property = Database::factory('EventTeam');
                if ($this->is_loaded()) {
                    $property = $property->join('EventTeam_Shooter')
                                ->on('EventTeam_Shooter', 'event_teams_shooters.team_id', '=', 'event_teams.id')
                                ->where('event_id', '=', $this->match->event_id)
                                ->where('shooter_id', '=', $this->shooter_id)
                                ->find();
                }
                break;*/
            }
        }

        return $property;
    }
    
        public function __toString()
    {
        return sprintf( '%d.%d', $this->score_whole, $this->score_partial );
    }
/*
    public function save() {
        $team_grade = Database::factory('TeamResult')
            ->where('team_id', '=', $this->team_id)
            ->where('grade_id', '=', $this->grade_id)
            ->find();
        if (!$team_grade->is_loaded()) {
            $team_grade->team_id = $this->team_id;
            $team_grade->grade_id = $this->grade_id;
            $team_grade->save();
        }
        return parent::save();
    }*/
}
