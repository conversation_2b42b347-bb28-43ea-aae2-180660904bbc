<?php
/**
 * Template Name: Results Template
 */
$match_id = get_post_meta(get_the_ID(), 'match_id', true);
$event_id = get_post_meta(get_the_ID(), 'event_id', true);
$subevent_id = get_post_meta(get_the_ID(), 'subevent_id', true);

$rmm = new ResultManagementModule(true);
$data = $rmm->gatherReportData(array(
  'match_id' => $match_id,
  'event_id' => $event_id,
  'subevent_id' => $subevent_id,
  'is_reversed' => true
));

get_header();
?>

<div class="results-page">
  <header class="results-header">
    <h1><?php the_title(); ?></h1>

    <?php
    while (have_posts()) {
      the_post();
      $content = get_the_content();
      if ($content)
        echo '<div class="results-description">' . $content . '</div>';
    }
    ?>

    <!-- Search and Filter Controls -->
    <div class="results-controls">
      <div class="search-container">
        <input type="text" id="results-search" placeholder="Search results by name, club, or state..." class="search-bar">
        <button type="button" id="clear-search" class="ui-button" style="display: none;">Clear</button>
      </div>

      <div class="filter-container">
        <label for="match-filter">Filter by Match:</label>
        <select id="match-filter">
          <option value="">All Matches</option>
          <?php foreach ($data['matches'] as $heading => $match): ?>
            <?php if ($match->is_published): ?>
              <option value="<?php echo esc_attr($heading); ?>"><?php echo esc_html($heading); ?></option>
            <?php endif; ?>
          <?php endforeach; ?>
        </select>

        <button type="button" id="expand-all" class="ui-button">Expand All</button>
        <button type="button" id="collapse-all" class="ui-button">Collapse All</button>
      </div>
    </div>
  </header>

  <div class="results-content">

    <?php foreach ($data['matches'] as $heading => $match):
      if (!$match->is_published) continue; ?>
      <div class="collapsible match-result" data-match="<?php echo esc_attr($heading); ?>" style="margin-bottom: 5px;">
        <h2>
          <span class="ui-collapsible-icon ui-collapsible-icon-d"></span>
          <?php echo $heading; ?>
          <?php if ($match->is_cancelled): ?>
            <span class="match-status cancelled">Cancelled</span>
          <?php else: ?>
            <span class="match-status published">Published</span>
          <?php endif; ?>
          <span class="match-info">
            <?php if ($match->event->is_team_event): ?>
              <span class="badge team-event">Team Event</span>
            <?php endif; ?>
            <?php if ($match->is_enter_shots): ?>
              <span class="badge shots-recorded">Shots Recorded</span>
            <?php endif; ?>
          </span>
        </h2>

    <?php if ($match->is_cancelled): ?>
      <table>
        <thead>
        <tr>
          <th><?php echo $match->cancel_reason; ?></th>
        </tr>
        </thead>
      </table>
    <?php else: ?>
      <?php if ($match->event->is_team_event): ?>
        <table>
          <thead>
          <tr>
            <th>Team Name</th>
            <th>Captain</th>
            <th>Coaches</th>
            <th>Discipline</th>
            <th>Score</th>
          </tr>
          </thead>
          <tbody>
          <?php foreach ($match->event->teams as $team):
            $teamRes = $team->getResultByMatchId($match->id); ?>
            <tr>
              <td><?php echo $team->name; ?></td>
              <td><?php echo $team->captain; ?></td>
              <td><?php echo $team->coach; ?></td>
              <td><?php printf("%s - %s", $teamRes->grade->discipline->name, $teamRes->grade->name); ?></td>
              <td><?php printf("%s.%s", $teamRes->score_whole, $teamRes->score_partial); ?></td>
            </tr>
          <?php endforeach; ?>
          </tbody>
        </table>
      <?php endif; ?>

        <div class="results-table-container">
          <table class="results-table data-table">
            <thead>
            <tr>
              <th width="5%" class="place-col"><?php _e('Place'); ?></th>
              <th width="15%" class="name-col"><?php _e('Last Name'); ?></th>
              <th width="15%" class="name-col"><?php _e('Preferred Name'); ?></th>
              <th width="<?php echo ($match->is_enter_shots) ? '30%' : '45%'; ?>" class="club-col"><?php _e('Club'); ?></th>
              <th width="10%" class="state-col"><?php _e('State'); ?></th>
              <?php if ($match->is_enter_shots): ?>
                <th width="15%" class="shots-col"><?php _e('Shots'); ?></th>
              <?php endif; ?>
              <th width="5%" class="info-col"><?php _e('Info'); ?></th>
              <th width="5%" class="score-col"><?php _e('Score'); ?></th>
            </tr>
            </thead>
            <tbody>
        <?php
        $data = $match->event->is_divisional ? array((object)array('divisions' => $match->getDivisionalReportData())) : $match->getReportData();
        foreach ($data as $discipline):
          $innerData = $match->event->is_divisional ? $discipline->divisions : $discipline->grades; ?>
          <?php foreach ($innerData as $item): ?>
          <?php if (count($item->results) === 0) continue; ?>
          <tr>
            <th colspan="8">
              <?php if ($match->event->is_divisional): ?>
                <?php echo $item->long_name; ?>
              <?php else: ?>
                <?php printf("%s - %s", $discipline->name, $item->name); ?>
              <?php endif; ?>
            </th>
          </tr>
            <?php foreach ($item->results as $result): ?>
              <tr class="result-row"
                  data-name="<?php echo esc_attr(strtolower($result->shooter->details->last_name . ' ' . $result->shooter->details->getPreferredName())); ?>"
                  data-club="<?php echo esc_attr(strtolower($result->shooter->club)); ?>"
                  data-state="<?php echo esc_attr(strtolower($result->shooter->getPrimaryAddress()->state)); ?>"
                  data-place="<?php echo esc_attr($result->place); ?>">
                <td class="place-cell" align="center">
                  <?php if ($result->place <= 3 && $result->place > 0): ?>
                    <span class="place-badge place-<?php echo $result->place; ?>"><?php echo $result->place; ?></span>
                  <?php else: ?>
                    <?php echo $result->place; ?>
                  <?php endif; ?>
                </td>
                <td class="name-cell"><?php echo $result->shooter->details->last_name; ?></td>
                <td class="name-cell"><?php echo $result->shooter->details->getPreferredName(); ?></td>
                <td class="club-cell"><?php echo $result->shooter->club; ?></td>
                <td class="state-cell"><?php echo $result->shooter->getPrimaryAddress()->state; ?></td>
                <?php if ($match->is_enter_shots): ?>
                  <td class="shots-cell"><?php echo $result->shots; ?></td>
                <?php endif; ?>
                <td class="info-cell"><?php echo $result->shooterInfo; ?></td>
                <td class="score-cell">
                  <span class="score-value"><?php echo $result; ?></span>
                </td>
              </tr>
            <?php endforeach; ?>
        <?php endforeach; ?>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
      </div>
    <?php endforeach; ?>
  </div>
</div>

<?php get_footer(); ?>

<style>
/* Enhanced Results Page Styling */
.results-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.results-header {
  margin-bottom: 30px;
}

.results-description {
  margin: 15px 0;
  padding: 15px;
  background: #f8f9fa;
  border-left: 4px solid #23408E;
  border-radius: 4px;
}

.results-controls {
  background: #fff;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-container {
  margin-bottom: 15px;
}

.search-container input[type="text"] {
  width: 300px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.filter-container label {
  font-weight: bold;
  color: #23408E;
}

.filter-container select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.match-result {
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.match-result h2 {
  background: linear-gradient(135deg, #23408E 0%, #475876 100%);
  color: white;
  margin: 0;
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background 0.3s ease;
}

.match-result h2:hover {
  background: linear-gradient(135deg, #1a2f6b 0%, #3a4a5e 100%);
}

.ui-collapsible-icon {
  margin-right: 10px;
  transition: transform 0.3s ease;
}

.match-result.collapsed .ui-collapsible-icon {
  transform: rotate(-90deg);
}

.match-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.match-status.published {
  background: #4CAF50;
  color: white;
}

.match-status.cancelled {
  background: #f44336;
  color: white;
}

.match-info {
  display: flex;
  gap: 8px;
}

.badge {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.badge.team-event {
  background: #FF9800;
  color: white;
}

.badge.shots-recorded {
  background: #2196F3;
  color: white;
}

.results-table-container {
  background: white;
}

.results-table {
  margin: 0;
  border-radius: 0;
}

.results-table th {
  background: #f8f9fa;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.place-badge {
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  text-align: center;
  line-height: 24px;
  font-weight: bold;
  color: white;
  font-size: 12px;
}

.place-badge.place-1 {
  background: #FFD700;
  color: #333;
}

.place-badge.place-2 {
  background: #C0C0C0;
  color: #333;
}

.place-badge.place-3 {
  background: #CD7F32;
  color: white;
}

.result-row:hover {
  background-color: #f0f8ff !important;
}

.score-value {
  font-weight: bold;
  color: #23408E;
}

.no-results-message {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

@media (max-width: 768px) {
  .results-controls {
    padding: 15px;
  }

  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container input[type="text"] {
    width: 100%;
  }

  .filter-container select {
    min-width: auto;
    width: 100%;
  }

  .match-result h2 {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .match-info {
    align-self: flex-end;
  }
}
</style>

<script>
$(document).ready(function() {
  // Initialize collapsible functionality
  function initializeCollapsible() {
    var $headers = $('.collapsible');
    $headers.addClass('collapsed');
    $headers.children(':not(h2)').hide();

    // Click handler for collapsible headers
    $headers.find('h2').click(function() {
      var $collapsible = $(this).parent();
      var $content = $collapsible.children(':not(h2)');
      var $icon = $(this).find('.ui-collapsible-icon');

      if ($collapsible.hasClass('collapsed')) {
        $content.slideDown(300);
        $collapsible.removeClass('collapsed');
        $icon.removeClass('ui-collapsible-icon-d');
      } else {
        $content.slideUp(300);
        $collapsible.addClass('collapsed');
        $icon.addClass('ui-collapsible-icon-d');
      }
    });
  }

  // Search functionality
  function initializeSearch() {
    var $searchInput = $('#results-search');
    var $clearButton = $('#clear-search');
    var searchTimeout;

    function performSearch() {
      var searchTerm = $searchInput.val().toLowerCase().trim();
      var $rows = $('.result-row');
      var visibleCount = 0;

      if (searchTerm === '') {
        $rows.show();
        $clearButton.hide();
        $('.no-results-message').remove();
        updateMatchVisibility();
        return;
      }

      $clearButton.show();

      $rows.each(function() {
        var $row = $(this);
        var name = $row.data('name') || '';
        var club = $row.data('club') || '';
        var state = $row.data('state') || '';
        var searchText = (name + ' ' + club + ' ' + state).toLowerCase();

        if (searchText.indexOf(searchTerm) !== -1) {
          $row.show();
          visibleCount++;
        } else {
          $row.hide();
        }
      });

      updateMatchVisibility();

      if (visibleCount === 0) {
        showNoResultsMessage();
      } else {
        $('.no-results-message').remove();
      }
    }

    $searchInput.on('input', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(performSearch, 300);
    });

    $clearButton.click(function() {
      $searchInput.val('');
      performSearch();
    });
  }

  // Match filter functionality
  function initializeMatchFilter() {
    var $matchFilter = $('#match-filter');

    $matchFilter.change(function() {
      var selectedMatch = $(this).val();
      var $matches = $('.match-result');

      if (selectedMatch === '') {
        $matches.show();
      } else {
        $matches.each(function() {
          var $match = $(this);
          if ($match.data('match') === selectedMatch) {
            $match.show();
          } else {
            $match.hide();
          }
        });
      }
    });
  }

  // Expand/Collapse all functionality
  function initializeExpandCollapse() {
    $('#expand-all').click(function() {
      var $collapsibles = $('.collapsible:visible');
      $collapsibles.removeClass('collapsed');
      $collapsibles.children(':not(h2)').slideDown(300);
      $collapsibles.find('.ui-collapsible-icon').removeClass('ui-collapsible-icon-d');
    });

    $('#collapse-all').click(function() {
      var $collapsibles = $('.collapsible:visible');
      $collapsibles.addClass('collapsed');
      $collapsibles.children(':not(h2)').slideUp(300);
      $collapsibles.find('.ui-collapsible-icon').addClass('ui-collapsible-icon-d');
    });
  }

  // Update match visibility based on visible results
  function updateMatchVisibility() {
    $('.match-result').each(function() {
      var $match = $(this);
      var $visibleRows = $match.find('.result-row:visible');
      var $visibleSections = $match.find('tr:has(.result-row:visible)').prev('tr');

      if ($visibleRows.length > 0) {
        $match.show();
        // Show section headers that have visible results
        $match.find('tbody tr').each(function() {
          var $row = $(this);
          if ($row.find('th[colspan]').length > 0) {
            // This is a section header
            var $nextRows = $row.nextUntil('tr:has(th[colspan])');
            var hasVisibleResults = $nextRows.filter(':has(.result-row:visible)').length > 0;
            if (hasVisibleResults) {
              $row.show();
            } else {
              $row.hide();
            }
          }
        });
      } else {
        // Hide match if no results are visible and we're searching
        if ($('#results-search').val().trim() !== '') {
          $match.hide();
        }
      }
    });
  }

  // Show no results message
  function showNoResultsMessage() {
    if ($('.no-results-message').length === 0) {
      $('.results-content').append(
        '<div class="no-results-message">' +
        '<h3>No results found</h3>' +
        '<p>Try adjusting your search terms or filters.</p>' +
        '</div>'
      );
    }
  }

  // Initialize all functionality
  initializeCollapsible();
  initializeSearch();
  initializeMatchFilter();
  initializeExpandCollapse();

  // Add loading animation for better UX
  $(document).ajaxStart(function() {
    $('body').addClass('loading');
  }).ajaxStop(function() {
    $('body').removeClass('loading');
  });
});
</script>
