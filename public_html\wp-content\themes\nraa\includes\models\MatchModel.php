<?php defined('ABSPATH') or die('No direct script access.');

/**
 * MatchModel
 *
 * @property bool is_aggregate
 * @property bool is_enter_shots
 * @property \Range range
 * @property array range_ids
 * @property array missing_shooters
 *
 * <AUTHOR> Group Pty Ltd
 */
class MatchModel extends MatchDatabase {
  protected $_tablename = 'matches';
  protected $_belongs_to = array(
    'event' => array(
      'model' => 'Event',
      'foreign_key' => 'event_id',
    ),
  );
  protected $_has_many = array(
    'ranges' => array(
      'model' => 'Range',
      'through' => 'Match_Range',
      'primary_key' => 'match_id',
      'foreign_key' => 'range_id',
    ),
    'results' => array(
      'model' => 'Result',
      'foreign_key' => 'match_id',
    ),
    'badges' => array(
      'model' => 'Match_Badge',
      'foreign_key' => 'match_id',
    ),
  );

  /**
   * Overloaded get method
   */
  public function &__get($key) {
    $property =& parent::__get($key);

    if ($property === null) {
      switch ($key) {
        case 'is_cancelled':
          $property = false;
          break;

        case 'is_aggregate':
          $property = count($this->ranges) > 1;
          break;

        case 'is_enter_shots':
          if ($this->is_aggregate) {
            $property = false;
          } else if ($this->range->subevent->id) {
            $property = $this->range->subevent->is_enter_shots;
          } else {
            $property = $this->event->is_enter_shots;
          }
          break;

        case 'range':
          if ($this->is_aggregate) {
            throw new UnexpectedValueException('Tried to get single range out of an aggregate match.');
          } else if (count($this->ranges) === 0) {
            throw new UnexpectedValueException('This match does not have any ranges and that should not be possible.' . $this->_last_sql);
          }
          $arr = array_values($this->ranges);
          $property = array_pop($arr);
          break;

        case 'range_ids':
          $property = array();
          foreach ($this->ranges as $range)
            $property[] = $range->id;
          break;

        case 'missing_shooters':
          $property = array();
          if ($this->is_aggregate)
            break;

          if ($this->event->is_using_entry_form) {
            foreach ($this->event->entry_forms as $entryForm) {
              if ($entryForm->status == 'withdrawn')
                continue;

              if (count($entryForm->subeventEntries) > 0) {
                $subevent_id = $this->range->subevent->id;
                $subeventEntry = array_filter($entryForm->subeventEntries, function ($entry) use ($subevent_id) {
                  return $entry->subevent_id == $subevent_id;
                });
                if (!$subeventEntry)
                  continue;
              }
              $shooter_id = $entryForm->shooter_id;
              $shooter = array_filter($this->results, function ($result) use ($shooter_id) {
                return $result->shooter_id == $shooter_id;
              });
              if (!$shooter) {
                $entryForm->shooter->entry_number = $entryForm->entry_number;
                $property[] = $entryForm->shooter;
              }
            }
          } else if ($this->number > 1) {
            $property = Database::factory('Shooter')
              ->join('Result')
              ->on('Result', 'results.shooter_id', '=', 'shooters.id')
              ->join('MatchModel')
              ->on('MatchModel', 'results.match_id', '=', 'matches.id')
              ->where('matches.event_id', '=', $this->event_id)
              ->where('matches.number', '<', $this->number)
              ->where('shooters.id', 'NOT IN', '(SELECT shooter_id FROM results WHERE match_id = ' . $this->id . ')')
              ->find_all();
          }
          break;
      }
    }

    return $property;
  }

  /**
   * Overloaded delete method
   */
  public function delete() {
    $this->removeAllRanges();

    return parent::delete();
  }

  /**
   * Removes all the Ranges from this Match
   *
   * @return \MatchModel
   * @throws InvalidArgumentException
   */
  public function removeAllRanges() {
    Database::factory('Match_Range')
      ->where('match_id', '=', $this->id)
      ->delete();

    return $this;
  }

  public function hasNonPlacedResults() {
    return Database::factory('Result')
      ->join('MatchModel', null, true)
      ->on('MatchModel', 'matches.id', '=', 'results.match_id')
      ->where('match_id', '=', $this->id)
      ->where('place', '=', '0')
      ->count_all() > 0;
  }

  public function hasResults() {
    return Database::factory('Result')
      ->where('match_id', '=', $this->id)
      ->count_all() > 0;
  }

  public function getIndividualWinner() {
    // Note: This assumes that the user has calculated places
    return Database::factory('Shooter')
      ->join('Result')
      ->on('Result', 'results.shooter_id', '=', 'shooters.id')
      ->where('results.match_id', '=', $this->id)
      ->where('results.place', '=', 1)
      ->find();
  }

  public function getIndividualRunnerup() {
    // Note: This assumes that the user has calculated places
    return Database::factory('Shooter')
      ->join('Result')
      ->on('Result', 'results.shooter_id', '=', 'shooters.id')
      ->where('results.match_id', '=', $this->id)
      ->where('results.place', '=', 2)
      ->find();
  }

  public function getTeamWinner() {
    // Note: This assumes that the user has calculated places
    return Database::factory('EventTeam')
      ->join('TeamResult')
      ->on('TeamResult', 'event_teams.id', '=', 'team_results.team_id')
      ->where('team_results.match_id', '=', $this->id)
      ->where('team_results.place', '=', 1)
      ->find();
  }

  public function getTeamRunnerup() {
    // Note: This assumes that the user has calculated places
    return Database::factory('EventTeam')
      ->join('TeamResult')
      ->on('TeamResult', 'event_teams.id', '=', 'team_results.team_id')
      ->where('team_results.match_id', '=', $this->id)
      ->where('team_results.place', '=', 2)
      ->find();
  }

  public function getAllResults() {
    return Database::factory('Result')
      ->join('MatchModel', null, true)
      ->on('MatchModel', 'matches.id', '=', 'results.match_id')
      ->join('Match_Range', null, false, 'mr1')
      ->on('mr1', 'mr1.match_id', '=', 'results.match_id')
      ->join('Match_Range', null, false, 'mr2')
      ->on('mr2', 'mr2.match_id', '=', $this->id)
      ->on('mr2', 'mr1.range_id', '=', 'mr2.range_id')
      ->order_by('matches.number', 'DESC')
      ->find_all();
  }

  public function getReportData($limit = false) {
    /*
    Structure:
    array(
        '[discipline_id]' => Discipline (
            ...[discipline data]...
            'grades' => array(
                '[grade_id]' => Grade (
                    ...[grade data]...
                    'results' => array(
                        '[result_id]' => Result(
                            ...[result data]...
                        ),
                        ...[more result objects]...
                    )
                ),
                ...[more grade objects]...
            )
        ),
        ...[more discipline objects]...
    )
     */
    $report_data = array();

    // Things that need to be accomplished:
    // - Find all disciplines with results for this match
    // - Find all grades with results for this match
    // - Partition all results by their discipline and grade
    /* FIXME: This is NEARLY the equivalent code using the ORM however we need to test this before using it
    $reportData = Database::factory('Discipline')
        ->join('Grade', null, true)
        ->on('Grade', 'grades.discipline_id', '=', 'disciplines.id')
        ->join('Result', null, true)
        ->on('Result', 'results.grade_id', '=', 'grades.id')
        ->join('MatchModel', null, true)
        ->on('MatchModel', 'matches.id', '=', 'results.match_id')
        ->join('Event', null, true)
        ->on('Event', 'events.id', '=', 'matches.event_id')
        ->join('Shooter', null, true)
        ->on('Shooter', 'shooters.id', '=', 'results.shooter_id')
        ->join('ShooterDetails', null, true)
        ->order_by('disciplines.id', 'ASC')
        ->order_by('grades.name', 'ASC')
        ->order_by('results.place', 'ASC');
    if ($limit) {
        $reportData->where('results.place', '>=', 1);
        $reportData->where('results.place', '<=', $limit);
    }
    $reportData = $reportData->find_all();
     */
    $sql = <<<'EOT'
SELECT
    `disciplines`.`id` AS 'discipline_id',
    `disciplines`.`name` AS 'discipline_name',
    `disciplines`.`short_name` AS 'discipline_short_name',
    `grades`.`id` AS 'grade_id',
    `grades`.`name` AS 'grade_name',
    `results`.`id` AS 'result_id',
    `results`.`place` AS 'result_place',
    `results`.`shots` AS 'result_shots',
    `results`.`score_whole` AS 'result_score_whole',
    `results`.`score_partial` AS 'result_score_partial',
    `shooters`.`id` AS 'shooter_id',
    `shooters`.`sid` AS 'shooter_sid',
    `shooters`.`club` AS 'shooter_club',
    `shooter_details`.`first_name` AS 'shooter_first_name',
    `shooter_details`.`last_name` AS 'shooter_last_name',
    `shooter_details`.`preferred_name` AS 'shooter_preferred_name',
    `shooter_details`.`date_of_birth` AS 'shooter_date_of_birth',
    `shooter_details`.`gender` AS 'shooter_gender'
FROM `disciplines`
JOIN `grades` ON `grades`.`discipline_id` = `disciplines`.`id`
JOIN `results` ON `results`.`grade_id` = `grades`.`id`
JOIN `matches` ON `matches`.`id` = `results`.`match_id`
JOIN `events` ON `events`.`id` = `matches`.`event_id`
JOIN `shooters` ON `shooters`.`id` = `results`.`shooter_id`
JOIN `shooter_details` ON `shooter_details`.`shooter_id` = `shooters`.`id`
WHERE `results`.`match_id` = ?
EOT;
    if ($limit) {
      $sql .= ' AND `results`.`place` >= ? AND `results`.`place` <= ?';
    }
    $sql .= ' ORDER BY `disciplines`.`id` ASC, `grades`.`name` ASC, `results`.`place` ASC';
    if ($stmt = self::getInstance()->prepare($sql)) {
      if ($limit) {
        $one = 1;
        $stmt->bind_param('iii', $this->id, $one, $limit);
      } else {
        $stmt->bind_param('i', $this->id);
      }
      $stmt->execute();
      $stmt->bind_result(
        $discipline_id, $discipline_name, $discipline_short_name,
        $grade_id, $grade_name,
        $result_id, $result_place, $result_shots, $result_score_whole, $result_score_partial,
        $shooter_id, $shooter_sid, $shooter_club, $shooter_first_name, $shooter_last_name, $shooter_preferred_name, $shooter_date_of_birth, $shooter_gender
      );

      if ($stmt->error)
        throw new mysqli_sql_exception('Error occurred while fetching data for report: ***' . $stmt->error . '***');

      $shooters = array();
      while ($stmt->fetch()) {
        if (!isset($shooters[$shooter_id])) {
          $details = new ShooterDetails($shooter_id);
          $details->set_is_loaded(true);
          $details->first_name = $shooter_first_name;
          $details->last_name = $shooter_last_name;
          $details->preferred_name = $shooter_preferred_name;
          $details->gender = $shooter_gender;
          $details->date_of_birth = $shooter_date_of_birth;

          $shooter = new Shooter($shooter_id);
          $shooter->set_is_loaded(true);
          $shooter->sid = $shooter_sid;
          $shooter->club = $shooter_club;
          $shooter->details = $details;
          $shooters[$shooter_id] = $shooter;
        } else {
          $shooter = $shooters[$shooter_id];
        }

        if (!isset($report_data[$discipline_id])) {
          $discipline = new Discipline($discipline_id);
          $discipline->set_is_loaded(true);
          $discipline->name = $discipline_name;
          $discipline->short_name = $discipline_short_name;
          $discipline->grades = array();
          $report_data[$discipline_id] = $discipline;
        } else {
          $discipline = $report_data[$discipline_id];
        }

        if (!isset($discipline->grades[$grade_id])) {
          $grade = new Grade($grade_id);
          $grade->set_is_loaded(true);
          $grade->name = $grade_name;
          $grade->results = array();
          $grade->discipline = $discipline;
          $discipline->grades[$grade_id] = $grade;
        } else {
          $grade = $discipline->grades[$grade_id];
        }

        $result = new Result($result_id);
        $result->set_is_loaded(true);
        $result->match = $this;
        $result->shooter = $shooter;
        $result->grade = $grade;
        $result->place = $result_place;
        $result->shots = $result_shots;
        $result->score_whole = $result_score_whole;
        $result->score_partial = $result_score_partial;
        $grade->results[$result_id] = $result;
      }
      $stmt->close();
    }

    return $report_data;
  }

  public function getDivisionalReportData() {
    $divOpen = new \Division();
    $divOpen->long_name = "Division Open";
    $divOpen->short_name = "Div Open";
    $divFStandOpen = new \Division();
    $divFStandOpen->long_name = "Division F Standard Open";
    $divFStandOpen->short_name = "Div F-S Open";
    $divisions = Database::factory('Division')
      ->order_by('order', 'asc')
      ->find_all();
    array_unshift($divisions, $divFStandOpen);
    array_unshift($divisions, $divOpen);

    $subeventId = null;
    if ($this->event->has_subevents) {
      $subeventId = $this->getSubEvent()->id;
    }
    foreach ($divisions as &$division) {
      if ($division->id) { // Normal Cases
        $division->results = Database::factory('Result')
          ->join('EventShooterDivision', null, true)
          ->on('EventShooterDivision', 'results.shooter_id', '=', 'event_shooter_divisions.shooter_id')
          ->where('results.match_id', '=', $this->id)
          ->where('event_shooter_divisions.division_id', '=', $division->id)
          ->order_by('results.place', 'ASC');
        if ($subeventId) {
          $division->results->where('event_shooter_divisions.subevent_id', '=', $subeventId);
        } else {
          $division->results->where('event_shooter_divisions.event_id', '=', $this->event_id);
        }
        $division->results = $division->results->find_all();
      } else { // Special Cases
        $results = Database::factory('Result')
          ->join('MatchModel', null, true)
          ->on('MatchModel', 'matches.id', '=', 'results.match_id')
          ->join('Grade', null, true)
          ->on('Grade', 'grades.id', '=', 'results.grade_id')
          ->join('Match_Range', null, true)
          ->on('Match_Range', 'match_ranges.match_id', '=', 'matches.id')
          ->join('Range', null, true)
          ->on('Range', 'ranges.id', '=', 'match_ranges.range_id')
          ->join('SubEvent_Range', null, true)
          ->on('SubEvent_Range', 'subevents_ranges.range_id', '=', 'ranges.id')
          ->join('SubEvent', null, true)
          ->on('SubEvent', 'subevents.id', '=', 'subevents_ranges.subevent_id')
          ->join('EventShooterDivision', null, true)
          ->on('EventShooterDivision', 'event_shooter_divisions.shooter_id', '=', 'results.shooter_id')
          ->on('EventShooterDivision', 'event_shooter_divisions.event_id', '=', 'matches.event_id')
          ->on_open('EventShooterDivision')
          ->on('EventShooterDivision', 'event_shooter_divisions.subevent_id', '=', 'subevents.id')
          ->or_on('EventShooterDivision', 'event_shooter_divisions.subevent_id', '=', 'NULL')
          ->on_close('EventShooterDivision')
          ->join('Division', null, true)
          ->on('Division', 'divisions.id', '=', 'event_shooter_divisions.division_id')
          ->where('results.match_id', '=', $this->id)
          ->order_by('score_whole', 'desc')
          ->order_by('score_partial', 'desc');
        if ($division->long_name == $divOpen->long_name) {
          $results = $results
            ->where('divisions.is_open', '=', 1)
            ->find_all();
        } else if ($division->long_name == $divFStandOpen->long_name) {
          $results = $results
            ->where('grades.discipline_id', '=', 2)
            ->find_all();
        } else {
          $results = array();
        }
        $idFunc = function ($r) { return 1; };
        $results = Result::CalculatePlaces($results, $idFunc);
        $results = Result::GroupResultsByIdFunc($results, $idFunc);
        $division->results = Result::UpdatePlacesWithCountBack($results, $this->getType());
      }
    }
    $divisions = array_filter($divisions, function ($div) {
      return count($div->results) > 0;
    });

    return $divisions;
  }

  public function getAllRelatedAggregates() {
    if ($this->is_aggregate) {
      throw new BadMethodCallException("This function currently only works for non-aggregate matches.");
    }

    $matches = Database::factory('MatchModel')
      ->join('Match_Range', null, true)
      ->on('Match_Range', 'matches.id', '=', 'match_ranges.match_id')
      ->where('match_ranges.range_id', '=', $this->range->id)
      ->where('match_id', '!=', $this->id)
      ->where('matches.event_id', '=', $this->event_id)
      ->order_by('matches.number', 'ASC')
      ->find_all();

    return $matches;
  }

  /**
   * Calls stored procedure to calculate aggregates
   *
   * @return bool wasSuccess?
   * @throws \NRAA\Exceptions\DatabaseException
   */
  public function calculateAggregate($allowPartialParticipation = false) {
    if (!$this->is_aggregate) {
      throw new BadMethodCallException("This only works on aggregate matches");
    }

    $divisional = ($this->event->is_divisional ? "Divisional" : "");
    $statement = self::getInstance()->prepare("CALL `Calculate${divisional}AggregateMatch`(?,?)");
    if ($statement) {
      $statement->bind_param('ii', $this->id, $allowPartialParticipation);
      $statement->execute();

      if ($statement->error) {
        throw new \NRAA\Exceptions\DatabaseException("Error occurred while calculatingAggregates: ({$statement->errno}) {$statement->error}");
      }
      return true;
    }
    return false;
  }

  /**
   * Get the categories of the Match
   *
   * @param  boolean $as_model_objects
   *
   * @return array   array(
   *     'subevents' => array( ..objects | ids.. ),
   *     'matches' => array( ..objects | ids.. ),
   *     'ranges' => array( ..objects | ids.. ),
   * )
   */
  public function getCategories($as_model_objects = false) {
    $categories = array(
      'subevents' => array(),
      'matches' => array(),
      'ranges' => array()
    );

    // Gather the range ids for this matches ranges
    // We will need this for comparisons
    $my_ranges = $this->getMyRangeIds();

    // Is a SubEvents ranges a subset of my ranges?
    $categories['subevents'] = $this->getSubEventCategory($as_model_objects, $my_ranges);

    // Is another Matches ranges a subset of my ranges?
    $categories['matches'] = $this->getMatchCategory($as_model_objects, $my_ranges);

    // Ranges are made up of whatever is left over
    if ($as_model_objects)
      foreach ($my_ranges as $id)
        $categories['ranges'][] = Database::factory('Range', $id)->find();
    else
      $categories['ranges'] = $my_ranges;

    return $categories;
  }

  /**
   * @return array
   */
  private function getMyRangeIds() {
    $my_ranges = $this->is_loaded()
      ? $this->getRangeIds($this->ranges)
      : array();

    return $my_ranges;
  }

  /**
   * Get all the ids of the ranges
   *
   * @param array $model_array
   *
   * @return array
   */
  public function getRangeIds(array $model_array) {
    return array_map(function ($range) {
      return $range->id;
    }, $model_array);
  }

  /**
   * @param $as_model_objects
   * @param $myRanges
   *
   * @return array
   */
  private function getSubEventCategory($as_model_objects, &$myRanges) {
    // We figure this out to know whether this whole SubEvent is part of the match
    $subEventCategory = array();
    $subevents = Database::factory('SubEvent')
      ->where('event_id', '=', $this->event_id)
      ->find_all();
    foreach ($subevents as $subevent) {
      $subevent_ranges = $this->getRangeIds($subevent->ranges);
      if (!array_diff($subevent_ranges, $myRanges)) {
        // Remove the ranges so they don't get matched again
        $myRanges = array_diff($myRanges, $subevent_ranges);
        $subEventCategory[] = $as_model_objects ? $subevent : $subevent->id;
      }
    }

    return $subEventCategory;
  }

  public function getMatchesOfAggregate() {
    if ($this->is_aggregate) {
      $rangeIds = $this->getMyRangeIds();
      return $this->getMatchCategory(true, $rangeIds);
    }
    return array();
  }

  public function getSubAggregates() {
    $matches = array();
    if ($this->is_aggregate) {
      $rangeIds = $this->getMyRangeIds();
      $matches = array_filter(
        Database::factory('MatchModel')
          ->where('event_id', '=', $this->event_id)
          ->where('number', '<', $this->number)
          ->order_by('number', 'asc')
          ->find_all(),
        function ($match) use ($rangeIds) {
          if (!$match->is_aggregate) return false;
          $match_ranges = $match->getRangeIds($match->ranges);
          return !array_diff($match_ranges, $rangeIds);
        });
    }
    return $matches;
  }

  /**
   * @param $as_model_objects
   * @param $myRanges
   *
   * @return array
   */
  private function getMatchCategory($as_model_objects, &$myRanges) {
    $matchCategory = array();
    $matches = Database::factory('MatchModel')
      ->where('event_id', '=', $this->event_id)
      ->where('number', '<', $this->number)
      ->order_by('number', 'asc')
      ->find_all();
    foreach ($matches as $match) {
      if (!$myRanges) break;

      $match_ranges = $this->getRangeIds($match->ranges);
      if (!array_diff($match_ranges, $myRanges)) {
        $myRanges = array_diff($myRanges, $match_ranges);
        $matchCategory[] = $as_model_objects ? $match : $match->id;
      }
    }

    return $matchCategory;
  }

  /**
   * Get the type of match.
   *
   * Types:
   * 0 - Unknown
   * 1 - Single Range
   * 2 - Daily Aggregate
   * 3 - Aggregate of daily aggregates (e.g. lead up or queens)
   *
   * @return integer
   */
  public function getType() {
    $categories = $this->getCategories(false);
    if (count($categories['matches']) > 1 || count($categories['subevents']) > 0)
      return 3;

    if (count($categories['ranges']) > 1)
      return 2;

    if (count($categories['ranges']) == 1)
      return 1;

    return 0;
  }

  /**
   * Get the next number in this event's match list
   *
   * @return int
   */
  public function getNextNumber() {
    $match = Database::factory('MatchModel')
      ->where('event_id', '=', $this->event_id)
      ->order_by('number', 'DESC')
      ->limit(1)
      ->find_all();
    $number = 1;
    if (count($match) > 0) {
      $match = array_shift($match);
      if ($match) {
        $number = (int)$match->number + 1;
      }
    }

    return $number;
  }

  /**
   * Add a subevent, match or range to this match.
   *
   * @param  string $type
   * @param  int $id
   *
   * @return \MatchModel
   * @throws InvalidArgumentException
   */
  public function add($type, $id) {
    if (!$id)
      throw new InvalidArgumentException("Unknown id: '$id' passed to add() function.");

    switch (strtolower($type)) {
      case 'subevent':
        $subevent = Database::factory('SubEvent', $id)->find();
        foreach ($subevent->ranges as $range)
          $this->addRange($range->id);
        break;

      case 'match':
        $match = Database::factory('MatchModel', $id)->find();
        foreach ($match->ranges as $range)
          $this->addRange($range->id);
        break;

      case 'range':
        $this->addRange($id);
        break;

      default:
        throw new InvalidArgumentException("Unknown type: '$type' passed to add() function.");
    }

    return $this;
  }

  /**
   * Add a Range to this match.
   *
   * @chainable
   *
   * @param  int $range_id
   *
   * @return \MatchModel                   or FALSE
   * @throws InvalidArgumentException
   */
  public function addRange($range_id) {
    // Check if this range is already in my list
    if (in_array($range_id, $this->range_ids))
      return $this;

    // Add range to DB
    Database::factory('Match_Range')
      ->values(array(
        'match_id' => $this->id,
        'range_id' => $range_id,
      ))
      ->save();

    // Add range to list
    $this->range_ids[] = $range_id;

    return $this;
  }

  public function getMatch() {
    return $this;
  }

  /**
   * Get the Top or Highest score for this match in the specified discipline
   *
   * @param $discipline_id
   *
   * @return \Result
   */
  public function getHighScoreResult($discipline_id) {
    $filteredResults = array_filter($this->results, function ($item) use ($discipline_id) {
      return $item->grade->discipline_id == $discipline_id;
    });

    usort($filteredResults, function ($a, $b) {
      if ($a->score_whole == $b->score_whole) {
        if ($a->score_partial == $b->score_partial)
          return 0;

        return $a->score_partial < $b->score_partial ? 1 : -1;
      }

      return $a->score_whole < $b->score_whole ? 1 : -1;
    });

    return array_shift($filteredResults);
  }

  /**
   * Determine if this match is a multi subevent aggregate
   *
   * @return bool
   */
  public function isMultiSubEventsAggregate() {
    return count($this->getSubEvents()) > 1;
  }

  /**
   * Get the subevent associated with this match
   *
   * @return \SubEvent|null
   */
  public function getSubEvent() {
    if (!$this->event->has_subevents || count($this->ranges) === 0)
      return null;

    $arr = array_values($this->ranges);
    $range = array_pop($arr);
    return $range->subevent;
  }

  /**
   * @param bool $modelObject
   *
   * @return array
   */
  public function getSubEvents($modelObject = false) {
    if (!$this->is_aggregate)
      return null;

    $rangeIds = $this->getMyRangeIds();
    return $this->getSubEventCategory($modelObject, $rangeIds);
  }

  /**
   * Get a badge by the grade id
   *
   * @param int $grade_id
   *
   * @return \Match_Badge
   */
  public function getBadge($grade_id, $byGrade = true) {
    foreach ($this->badges as $badge) {
      if ($byGrade && $badge->grade_id == $grade_id)
        return $badge;
      else if (!$byGrade && $badge->division_id == $grade_id)
        return $badge;
    }

    return null;
  }

  public function removeAllBadges() {
    Database::factory('Match_Badge')
      ->where('match_id', '=', $this->id)
      ->delete();
  }

  public function __toString()
  {
    if ($this->name)
      return sprintf("%s", $this->name);

    return sprintf("Match %d", $this->number);
  }

  public function getComplexName() {
    if ($this->name)
      return $this->name;

    if ($this->is_aggregate)
      return 'Aggregate';

    if (count($this->ranges) > 0)
      return current($this->ranges)->__toString();

    return sprintf("Match %d", $this->number);
  }
}
