<?php defined('ABSPATH') or die('No direct script access.');

/**
 * Model for NRAA Events
 *
 * <AUTHOR> Group Pty Ltd
 */
class Event extends MatchDatabase
{
  public $column_long_names = array(
    'id'                 => 'ID',
    'name'               => 'Name',
    'start_date'         => 'Start Date',
    'end_date'           => 'End Date',
    'start_date_display' => 'Start Date',
    'end_date_display'   => 'End Date',
  );
  protected $_tablename = "events";
  protected $_belongs_to = array(
    'association' => array(
      'model'       => 'Association',
      'foreign_key' => 'association_id',
    )
  );
  protected $_has_many = array(
    'ranges'          => array(
      'model'       => 'Range',
      'through'     => 'Event_Range',
      'foreign_key' => 'range_id',
      'primary_key' => 'event_id',
    ),
    'subevents'       => array(
      'model'       => 'SubEvent',
      'foreign_key' => 'event_id',
    ),
    'teams'           => array(
      'model'       => 'EventTeam',
      'foreign_key' => 'event_id',
    ),
    'matches'         => array(
      'model'       => 'MatchModel',
      'foreign_key' => 'event_id',
      'order_by'    => array('number' => null)
    ),
    'entry_forms'     => array(
      'model'       => 'EventEntryForm',
      'foreign_key' => 'event_id'
    ),
    'grade_overrides' => array(
      'model'       => 'ShooterGradeOverrides',
      'foreign_key' => 'event_id',
    ),
    'presentation_orderings' => array(
      'model' => 'EventPresentationOrdering',
      'foreign_key' => 'event_id'
    )
  );
  protected $_has_one = array(
    'entry_form_settings' => array(
      'model'       => 'EventEntryFormSetting',
      'foreign_key' => 'event_id'
    ),
  );

  public function &__get($key)
  {
    $property =& parent::__get($key);

    if ($property === null) {
      switch ($key) {
        case 'results':
          $property = $this->findResults()->find_all();
          break;

        case 'has_results':
          $property = $this->findResults()->count_all() > 0;
          break;

        case 'start_date_display':
          $property = $this->start_date ? date('d-m-Y', $this->start_date) : 'TBA';
          break;

        case 'end_date_display':
          $property = $this->end_date ? date('d-m-Y', $this->end_date) : 'TBA';
          break;

        case 'is_using_entry_form':
          if (!$this->is_loaded()) {
            return false;
          }

          $count = Database::factory('EventEntryFormSetting')
            ->where('event_id', '=', $this->id)
            ->count_all();

          $property = $count > 0;
          break;

        case 'has_subevents':
          $property = count($this->subevents) > 0;
          break;

        case 'is_competition': //- Default case if this is a new event
          $property = true;
          break;
      }
    }

    return $property;
  }

  public function getMatch()
  {
    $ranges = $this->ranges;
    if ($this->has_subevents) {
      $ranges = array();
      foreach ($this->subevents as $subevent) {
        $ranges = array_merge($ranges, $subevent->ranges);
      }
    }

    return $this->getMatchForRanges($ranges);
  }

  public function deletePresentationOrderings()
  {
    return Database::factory('EventPresentationOrdering')
      ->where('event_id', '=', $this->id)
      ->delete();
  }

  /**
   * Count how many events in the past without any results
   *
   * @return int
   */
  public function countPastEventsWithoutResults()
  {
    return Database::factory('Event')
      ->join('Match')
      ->on('Match', 'matches.event_id', '=', 'events.id')
      ->join('Result', 'LEFT OUTER')
      ->on('Result', 'results.match_id', '=', 'matches.id')
      ->where('events.end_date', '<', time())
      ->where('results.id', 'IS', 'NULL')
      ->count_all();
  }

  /**
   * Add a range to the subevent
   *
   * @chainable
   *
   * @param  Range $range
   *
   * @return \SubEvent
   */
  public function addRange(Range $range)
  {
    // Check if this event has any subevents
    if (count($this->subevents) > 0)
      return false;

    Database::factory('Event_Range')
      ->values(array(
        'event_id' => $this->id,
        'range_id' => $range->id
      ))
      ->save();

    return $this;
  }

  /**
   * Remove a range from the subevent
   *
   * @chainable
   *
   * @param  Range $range
   *
   * @return \SubEvent
   */
  public function removeRange(Range $range)
  {
    Database::factory('Event_Range')
      ->where('event_id', '=', $this->id)
      ->where('range_id', '=', $range->id)
      ->limit(1)
      ->delete();

    return $this;
  }

  public function save()
  {
    if (isset($this->_object['extraData'])) {
      $extraData = json_encode($this->_object['extraData']);
      if ($extraData != $this->extra_data)
        $this->extra_data = $extraData;
      unset($this->_object['extraData']);
    }
    return parent::save();
  }

  /**
   * Clean up relations
   */
  public function delete()
  {
    if ($this->id) {
      foreach ($this->subevents as $subevent)
        $subevent->delete();

      foreach ($this->ranges as $range)
        $range->delete();

      foreach ($this->teams as $team)
        $team->delete();

      foreach ($this->matches as $match)
        $match->delete();
    }

    return parent::delete();
  }

  /**
   * Remove all ranges from the subevent
   *
   * @chainable
   * @return \SubEvent
   */
  public function removeAllRanges()
  {
    Database::factory('Event_Range')
      ->where('event_id', '=', $this->id)
      ->delete();

    return $this;
  }

  /**
   * Prepare a database object with the results of this Event
   *
   * @return \Event
   */
  protected function findResults()
  {
    return Database::factory('Result')
      ->join('Match', null, true)
      ->on('Match', 'results.match_id', '=', 'matches.id')
      ->where('matches.event_id', '=', $this->id);
  }
}
