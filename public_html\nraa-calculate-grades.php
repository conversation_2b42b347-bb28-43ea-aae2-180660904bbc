<?php
if (isset($_GET['_NRAA_DEBUG']) && $_GET['_NRAA_DEBUG'] == 'devNraa2020') {
  phpinfo();
  exit();
}
?>
<html>
<head>
  <title>Calculating Grades</title>
</head>
<body>
  <h1>Calculating Grades</h1>
  <p>This can take a very long time so please be patient.</p>
  <ol>
<?php
/**
 * Recalculate all shooter grades.
 *
 * Writing this to replace the horrible SQL version.
 *
 * Date: 29/07/2016 (Updated)
 *
 * <AUTHOR> <<EMAIL>>
 */
DATE_DEFAULT_TIMEZONE_SET("Australia/ACT");

require_once 'wp-load.php';

global $docroot;
$docroot = ABSPATH . '/wp-content/themes/nraa';

require_once $docroot . '/includes/autoload.php';
require_once $docroot . '/includes/export-utils.php';

setupErrorReporting();

/* Unleash the full powerrr!! :p */
@set_time_limit(0);
@ini_set('memory_limit', '-1');
@ignore_user_abort(true);

displayText("Starting up...");
displayText("Cleaning old grades...");
// Truncate table - Recalculate every day
Database::factory('ShooterCalculatedGrade')
  ->where('1', '=', '1')
  ->delete();

displayText("Finding high scores...");
// Find all the high scores for every match/division
$results = Database::factory('Result')
  ->join('Grade', null, true)
  ->on('Grade', 'results.grade_id', '=', 'grades.id')
  ->join('MatchModel', null, true)
  ->on('MatchModel', 'matches.id', '=', 'results.match_id')
  ->join('Event', null, true)
  ->on('Event', 'events.id', '=', 'matches.event_id')
  ->where('matches.is_graded', '=', 1)
  ->where('matches.is_cancelled', '=', 0)
  ->where('events.is_team_event', '=', 0)
  ->where('events.end_date', '>=', strtotime(GRADING_START_DATE))
  ->order_by('matches.id', 'ASC')
  ->order_by('grades.discipline_id', 'ASC')
  ->order_by('results.score_whole', 'DESC')
  ->order_by('results.score_partial', 'DESC')
  ->find_all();

$highScores = array();
foreach ($results as $result) {
  $discipline_id = $result->grade->discipline_id;
  $match_id = $result->match_id;
  if (!isset($highScores[$match_id])) {
    $highScores[$match_id] = array();
  }
  if (!isset($highScores[$match_id][$discipline_id])) {
    $highScores[$match_id][$discipline_id] = $result->score;
  }
}

// Cleanup memory and reuse variable
unset($results);

displayText("Calculating shooters grades...");
$temp = 0;
// Now calculate avg_score and number_of_shoots for shooter's discipline
$shooters = Database::factory('Shooter')->find_all();
foreach ($shooters as $shooter) {
  $results = Database::factory('Result')
    ->join('Grade', null, true)
    ->on('Grade', 'grades.id', '=', 'results.grade_id')
    ->join('Discipline', null, true)
    ->on('Discipline', 'disciplines.id', '=', 'grades.discipline_id')
    ->join('Match', null, true)
    ->on('Match', 'matches.id', '=', 'results.match_id')
    ->join('Event', null, true)
    ->on('Event', 'events.id', '=', 'matches.event_id')
    ->where('results.shooter_id', '=', $shooter->id)
    ->where('matches.is_graded', '=', 1)
    ->where('matches.is_cancelled', '=', 0)
    ->where('events.is_team_event', '=', 0)
    ->where('events.end_date', '>=', strtotime(GRADING_START_DATE))
    ->order_by('events.end_date', 'DESC')
    ->order_by('matches.number', 'DESC')
    ->find_all();

  if (count($results) === 0) {
    continue;
  }

  $discScorePercs = array();
  foreach ($results as $result) {
    $discipline_id = $result->grade->discipline_id;
    $match_id = $result->match_id;
    if (!isset($discScorePercs[$discipline_id])) {
      $discScorePercs[$discipline_id] = array();
    }

    $count = count($discScorePercs[$discipline_id]);
    if ($count >= MAX_SHOOTS) {
      continue;
    }

    if (!isset($highScores[$match_id][$discipline_id])) {
      displayText("No High Score?... You must have made a mistake! I'll print out the high scores variable for you");
      displayText(print_r($highScores, true));
      die;
    }
    $discScorePercs[$discipline_id][] = $result->score->percentageOf($highScores[$match_id][$discipline_id]);
  }

  foreach ($discScorePercs as $discipline_id => $results) {
    $count = count($results);
    if ($count < MIN_SHOOTS) continue;

    $avgScore = array_sum($results) / $count;
    $grade = Database::factory('Grade')
      ->where('discipline_id', '=', $discipline_id)
      ->where('threshold', '<=', $avgScore)
      ->order_by('threshold', 'DESC')
      ->find();

    $calculatedGrade = Database::factory('ShooterCalculatedGrade');
    $calculatedGrade->avg_score = $avgScore;
    $calculatedGrade->shooter_id = $shooter->id;
    $calculatedGrade->grade_id = $grade->id;
    $calculatedGrade->number_of_shoots = $count;
    $calculatedGrade->save();
  }
}

displayText("Done!");
file_put_contents('/tmp/last_grade_success.txt', date("Y-m-d H:i:s"));
?>
  </ol>
</body>
</html>
