<?php

/**
 * Count backs were briefly tested in ResultTest however more strict count back rules have been implemented so
 * these new tests are separated out for clarity.
 */
class CountBackTest extends PHPUnit\Framework\TestCase {
    /** @var array */
    protected static $tempData;

    public static function setUpBeforeClass(): void
    {
        $sid = 99999;
        $matchNumber = 0;
        $createShooter = function($letter) use (&$sid) {
            $shooterId = Database::factory('Shooter')
                ->values(array(
                    'sid' => --$sid,
                    'club' => 'Test Shooter Club'
                ))
                ->save()
                ->id;
            Database::factory('ShooterDetails')
                ->values(array(
                    'shooter_id' => $shooterId,
                    'first_name' => 'Shooter',
                    'last_name' => $letter
                ))
                ->save();
            return $shooterId;
        };
        $createRange = function($eventId, $letter) {
            $rangeId = Database::factory('Range')
                ->values(array(
                    'name' => 'Range ' . $letter,
                    'date' => time(),
                    'distance' => '300y',
                    'number_of_shots' => 6
                ))
                ->save()
                ->id;
            Database::factory('Event_Range')
                ->values(array('event_id' => $eventId, 'range_id' => $rangeId))
                ->save();
            return $rangeId;
        };
        $createMatch = function($eventId, array $rangeIds) use (&$matchNumber) {
            $match = Database::factory('MatchModel')
                ->values(array(
                    'event_id' => $eventId,
                    'number' => ++$matchNumber,
                    'name' => 'Match ' . $matchNumber,
                    'is_graded' => 0,
                    'is_published' => 0
                ))
                ->save();
            foreach ($rangeIds as $rangeId)
                $match->addRange($rangeId);
            return $match->id;
        };
        $createResult = function($matchId, $shooterId, $shots) {
            $scoreWhole = 0;
            $scorePartial = 0;
            $shots = strtoupper($shots);
            foreach (str_split($shots) as $shot) {
                if ($shot == 'V' || $shot == 'X') {
                    $scorePartial += 1;
                    $scoreWhole += $shot == 'V' ? 5 : 6;
                } else {
                    $scoreWhole += $shot;
                }
            }

            $result = Database::factory('Result')
                ->values(array(
                    'match_id' => $matchId,
                    'shooter_id' => $shooterId,
                    'grade_id' => 1,
                    'shots' => $shots,
                    'score_whole' => $scoreWhole,
                    'score_partial' => $scorePartial
                ))
                ->save()
                ->id;
            return $result;
        };

        /* Make sure previous test information is cleared */
        $temp = Database::factory('Shooter')
            ->where('sid', '>', 99994)
            ->where('sid', '<', 99999)
            ->find_all();
        foreach ($temp as $t) {
            foreach ($t->results as $r)
                $r->delete();
            $t->delete();
        }
        Database::factory('Event')
            ->where('name', '=', 'Test Event')
            ->delete();

        /* Create our Fictional Testing Event */
        $eventId = Database::factory('Event')
            ->values(array(
                'name' => 'Test Event',
                'start_date' => time(),
                'end_date' => time(),
                'is_enter_shots' => 1
            ))
            ->save()
            ->id;

        /* Create Our Fictional Testing Shooters */
        $shooterA = $createShooter('A');
        $shooterB = $createShooter('B');
        $shooterC = $createShooter('C');
        $shooterD = $createShooter('D');

        /* Create some ranges for the event */
        $rangeA = $createRange($eventId, 'A');
        $rangeB = $createRange($eventId, 'B');
        $rangeC = $createRange($eventId, 'C');
        $rangeD = $createRange($eventId, 'D');

        /* Create the matches of the event */
        $match1 = $createMatch($eventId, array($rangeA));
        $match2 = $createMatch($eventId, array($rangeB));
        $match3 = $createMatch($eventId, array($rangeA, $rangeB));
        $match4 = $createMatch($eventId, array($rangeC));
        $match5 = $createMatch($eventId, array($rangeD));
        $match6 = $createMatch($eventId, array($rangeC, $rangeD));
        $match7 = $createMatch($eventId, array($rangeA, $rangeB, $rangeC, $rangeD));

        /* Create results for this event */
        $result1 = $createResult($match1, $shooterA, '5VV5V5'); // 30.3
        $result2 = $createResult($match1, $shooterB, 'V5V5V5'); // 30.3
        $result3 = $createResult($match1, $shooterC, '45455V'); // 28.1
        $result4 = $createResult($match1, $shooterD, '45545V'); // 28.1

        $result5 = $createResult($match2, $shooterA, '5VV5V5'); // 30.3
        $result6 = $createResult($match2, $shooterB, 'V5V5V5'); // 30.3
        $result7 = $createResult($match2, $shooterC, '445454'); // 26.0
        $result8 = $createResult($match2, $shooterD, '355455'); // 27.0

        $result9 = $createResult($match4, $shooterA, '5VV5V5');  // 30.3
        $result10 = $createResult($match4, $shooterB, '5VV5V5'); // 30.3
        $result11 = $createResult($match4, $shooterC, '45455V'); // 28.1
        $result12 = $createResult($match4, $shooterD, '45455V'); // 28.1

        $result13 = $createResult($match5, $shooterA, '5VV5V5'); // 30.3
        $result14 = $createResult($match5, $shooterB, '5VV5V5'); // 30.3
        $result15 = $createResult($match5, $shooterC, '545545'); // 28.0
        $result16 = $createResult($match5, $shooterD, '544455'); // 27.0

        /* Save Ids to temp for easy removal later */
        self::$tempData = array(
            'shooterIds' => array($shooterA, $shooterB, $shooterC, $shooterD),
            'rangeIds' => array($rangeA, $rangeB, $rangeC, $rangeD),
            'matchIds' => array($match1, $match2, $match3, $match4, $match5, $match6, $match7),
            'resultIds' => array($result1, $result2, $result3, $result4, $result5, $result6, $result7, $result8, $result9, $result10, $result11, $result11, $result12, $result13, $result14, $result15, $result16),
            'eventId' => $eventId
        );
    }

    public static function tearDownAfterClass(): void
    {
        try {
            foreach (self::$tempData['shooterIds'] as $shooterId) {
                $shooter = Database::factory('Shooter', $shooterId)->find();
                foreach ($shooter->results as $result)
                    $result->delete();
                $shooter->delete();
            }

            Database::factory('Event', self::$tempData['eventId'])->delete();
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

    protected function debugInfo($results)
    {
        $debugInfo = array();
        foreach ($results as $result) {
            $debugInfo[] = array(
                'shooter' => $result->shooter->__toString(),
                'place' => $result->place,
                'shots' => $result->shots,
                'score' => $result->score->__toString()
            );
        }

        return print_r($debugInfo, true);
    }

    /**
     * If scores at single range are the same, then count back by shots, starting with the last.
     * E.G.        Shooter A (Target Rifle) – Score 55V,V5V,V5V5 = 50.5
     *             Shooter B (Target Rifle) – Score VVV,555,V5V5 = 50.5
     * Shooter A comes first – Starting from the last, the last 4 shots are the same then Shooter A has a V (5th
     * last shot) which beats the 5 that Shooter B has. For F Class the Count back procedure is the same – an “X” is
     * higher than a “6” and a 6 is higher than a 5. This procedure applies all the way down the prize list, whenever
     * two scores are the same.
     */
    public function testSingleRange()
    {
        // Arrange - In Setup
        $_GET['is_test'] = true;
        $_GET['match_id'] = self::$tempData['matchIds'][0];
        $_GET['event_id'] = self::$tempData['eventId'];

        // Act
        $rmm = new ResultManagementModule(true);
        $rmm->calc_places();
        $result1 = Database::factory('Result', self::$tempData['resultIds'][0])->find();
        $result2 = Database::factory('Result', self::$tempData['resultIds'][1])->find();
        $result3 = Database::factory('Result', self::$tempData['resultIds'][2])->find();
        $result4 = Database::factory('Result', self::$tempData['resultIds'][3])->find();

        // Assert
        $debugInfo = $this->debugInfo(array($result1, $result2, $result3, $result4));
        $this->assertEquals(1, $result1->place, $debugInfo);
        $this->assertEquals(2, $result2->place, $debugInfo);
        $this->assertEquals(3, $result3->place, $debugInfo);
        $this->assertEquals(4, $result4->place, $debugInfo);
    }

    /**
     * @depends testSingleRange
     */
    public function testDailyAggregate()
    {
        // Arrange - In Setup
        $_GET['is_test'] = true;
        $_GET['match_id'] = self::$tempData['matchIds'][1];
        $_GET['event_id'] = self::$tempData['eventId'];

        // Act
        $rmm = new ResultManagementModule(true);
        $rmm->calc_places();
        $_GET['match_id'] = self::$tempData['matchIds'][2];
        $rmm->calc_aggregates();
        $result1 = Database::factory('Result')
            ->where('shooter_id', '=', self::$tempData['shooterIds'][0])
            ->where('match_id', '=', $_GET['match_id'])
            ->find();
        $result2 = Database::factory('Result')
            ->where('shooter_id', '=', self::$tempData['shooterIds'][1])
            ->where('match_id', '=', $_GET['match_id'])
            ->find();
        $result3 = Database::factory('Result')
            ->where('shooter_id', '=', self::$tempData['shooterIds'][2])
            ->where('match_id', '=', $_GET['match_id'])
            ->find();
        $result4 = Database::factory('Result')
            ->where('shooter_id', '=', self::$tempData['shooterIds'][3])
            ->where('match_id', '=', $_GET['match_id'])
            ->find();

        // Assert
        $debugInfo = $this->debugInfo(array($result1, $result2, $result3, $result4));
        $this->assertEquals(1, $result1->place, $debugInfo);
        $this->assertEquals(1, $result2->place, $debugInfo);
        $this->assertEquals(4, $result3->place, $debugInfo);
        $this->assertEquals(3, $result4->place, $debugInfo);
    }

    /**
     * @depends testDailyAggregate
     */
    public function testAggregateOfDailyAggregates()
    {
        // Arrange - In Setup
        $_GET['is_test'] = true;
        $_GET['match_id'] = self::$tempData['matchIds'][3];
        $_GET['event_id'] = self::$tempData['eventId'];

        // Act
        $rmm = new ResultManagementModule(true);
        $rmm->calc_places();
        $_GET['match_id'] = self::$tempData['matchIds'][4];
        $rmm->calc_places();
        $_GET['match_id'] = self::$tempData['matchIds'][5];
        $rmm->calc_aggregates();
        $_GET['match_id'] = self::$tempData['matchIds'][6];
        $rmm->calc_aggregates();
        $result1 = Database::factory('Result')
            ->where('shooter_id', '=', self::$tempData['shooterIds'][0])
            ->where('match_id', '=', $_GET['match_id'])
            ->find();
        $result2 = Database::factory('Result')
            ->where('shooter_id', '=', self::$tempData['shooterIds'][1])
            ->where('match_id', '=', $_GET['match_id'])
            ->find();
        $result3 = Database::factory('Result')
            ->where('shooter_id', '=', self::$tempData['shooterIds'][2])
            ->where('match_id', '=', $_GET['match_id'])
            ->find();
        $result4 = Database::factory('Result')
            ->where('shooter_id', '=', self::$tempData['shooterIds'][3])
            ->where('match_id', '=', $_GET['match_id'])
            ->find();

        // Assert
        $debugInfo = $this->debugInfo(array($result1, $result2, $result3, $result4));
        $this->assertEquals(1, $result1->place, $debugInfo);
        $this->assertEquals(1, $result2->place, $debugInfo);
        $this->assertEquals(3, $result3->place, $debugInfo);
        $this->assertEquals(4, $result4->place, $debugInfo);
    }
}
