<?php defined('ABSPATH') or die('No direct script access.');

/**
 * Model for a NRAA Event Range
 *
 * <AUTHOR> Group Pty Ltd
 */
class Range extends MatchDatabase
{
  private $DISTANCE_LIST = array(
    '100y' => '100 yards',
    '200y' => '200 yards',
    '300y' => '300 yards',
    '400y' => '400 yards',
    '500y' => '500 yards',
    '600y' => '600 yards',
    '700y' => '700 yards',
    '800y' => '800 yards',
    '900y' => '900 yards',
    '1000y' => '1000 yards',
    '1100y' => '1100 yards',
    '1200y' => '1200 yards',
    '1500y' => '1500 yards',
    '100m' => '100 meters',
    '200m' => '200 meters',
    '300m' => '300 meters',
    '400m' => '400 meters',
    '500m' => '500 meters',
    '600m' => '600 meters',
    '700m' => '700 meters',
    '800m' => '800 meters',
    '900m' => '900 meters',
    '1000m' => '1000 meters',
    '1100m' => '1100 meters',
    '1200m' => '1200 meters',
    '1300m' => '1300 meters',
    '1400m' => '1400 meters',
    '1500m' => '1500 meters',
    'agg' => 'Aggregate'
  );
  protected $_tablename = "ranges";
  protected $_belongs_to = array(
    'event' => array(
      'model' => 'Event',
      'through' => 'Event_Range',
      'foreign_key' => 'event_id',
      'primary_key' => 'range_id',
    ),
    'subevent' => array(
      'model' => 'SubEvent',
      'through' => 'SubEvent_Range',
      'foreign_key' => 'subevent_id',
      'primary_key' => 'range_id',
    ),
    'match' => array(
      'model' => 'MatchModel',
      'through' => 'Match_Range',
      'foreign_key' => 'match_id',
      'primary_key' => 'range_id',
    ),
  );

  public function &__get($key)
  {
    $property =& parent::__get($key);

    if ($property === NULL) {
      switch ($key) {
        case 'distance_display':
          $property = $this->DISTANCE_LIST[$this->distance];
          break;

        case 'date_display':
          $property = $this->date ? date('d-m-Y', $this->date) : '';
          break;
      }
    }

    return $property;
  }

  /**
   * Overload this method because range doesn't have ranges it is the range.
   *
   * Get the match corresponding to this range.
   */
  public function getMatch()
  {
    return $this->match;
  }

  /**
   * Get a list of range distances
   *
   * @return array Range Distances
   */
  public function getRangeDistanceList()
  {
    return $this->DISTANCE_LIST;
  }

  public function delete()
  {
    // Delete any connections this range
    Database::factory(($this->subevent->id ? 'Sub' : '') . 'Event_Range')
      ->where('range_id', '=', $this->id)
      ->limit(1)
      ->delete();

    return parent::delete();
  }

  public function __toString()
  {
    if ($this->name)
      return $this->name;

    return $this->distance;
  }
}
