<?php defined('ABSPATH') or die('No direct script access.');

/**
 * Abstract class
 * - Models that have an associated Match will extend this
 *
 * <AUTHOR> Group Pty Ltd
 */
abstract class MatchDatabase extends Database
{
	abstract public function getMatch();

    /**
     * Get the match that is an aggregate of all given ranges
     *
     * @param $list_of_ranges
     *
     * @throws mysqli_sql_exception
     * @return MatchModel object
     */
	public function getMatchForRanges($list_of_ranges)
	{
		// Create a comma separated string of range ids for the SQL to follow
		$ranges = '';
		$range_count = 0;
		foreach ($list_of_ranges as $range) {
			$ranges .= $range->id . ',';
			$range_count++;
		}
		$ranges = rtrim($ranges, ','); // remove trailing comma
		// Find all matches that have exactly this list of ranges
		// There should only be one unless users have messed up.
		$sql =  "SELECT match_id ".
				"FROM match_ranges a ".
				"WHERE range_id IN ($ranges) ".
				"AND EXISTS ".
				"( ".
					"SELECT 1 ".
					"FROM match_ranges b ".
					"WHERE a.match_id = b.match_id ".
					"GROUP BY match_id ".
				") ".
				"GROUP BY match_id ".
				"HAVING COUNT(*) = $range_count";
		$match_id = null;
        if ($stmt = self::getInstance()->prepare($sql)) {
            $stmt->execute();
            $stmt->bind_result($match_id);
        }
        if ($stmt->error) {
            throw new mysqli_sql_exception("Error occurred while finding Match: ***{$stmt->error}***");
        }
        $stmt->fetch();
        $stmt->close();

		return Database::factory('MatchModel', $match_id);
	}
}
